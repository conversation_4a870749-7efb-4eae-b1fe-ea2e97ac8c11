"use client";

// src/app/(app)/directory/components/AddEntityWizard/hooks/useEntitySearch.ts

import { useState, useCallback, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import type { SearchResult } from '../AddEntityWizard.types';

import type { Id } from '@/../convex/_generated/dataModel';

interface UseEntitySearchProps {
  excludeIds?: (Id<"entities"> | string)[];
}

export const useEntitySearch = ({ excludeIds = [] }: UseEntitySearchProps = {}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search query
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Filter out temporary string IDs and keep only proper Convex IDs
  const excludeIdsForQuery = excludeIds.filter((id): id is Id<"entities"> => 
    typeof id !== 'string' || !id.startsWith('temp_')
  );

  // Use Convex query for search results
  const searchResults = useQuery(
    api.entities.entitiesQueries.searchEntities,
    debouncedQuery.trim() ? {
      searchTerm: debouncedQuery,
      excludeIds: excludeIdsForQuery
    } : "skip"
  );

  const isSearching = debouncedQuery !== searchQuery;

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setDebouncedQuery('');
  }, []);

  return {
    searchQuery,
    setSearchQuery,
    searchResults: searchResults ?? [],
    isSearching,
    clearSearch
  };
};