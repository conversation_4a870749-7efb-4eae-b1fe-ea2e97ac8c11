import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import type { Comment } from "../../types";

export const TaskComments = ({
  comments,
  taskId,
}: {
  comments: Comment[];
  taskId: string;
}) => {
  const [comment, setComment] = useState("");
  const createComment = useMutation(
    api.comments.commentsMutations.createComment,
  );

  const onCreateComment = async () => {
    setComment("");
    await createComment({
      parentId: taskId,
      content: comment,
    });
  };
  return (
    <Card className="mt-6 border border-white/20 bg-white/80 shadow-sm backdrop-blur-sm">
      <CardHeader className="pb-3">
        <h3 className="text-lg font-medium text-slate-800">Comments</h3>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="max-h-[300px] overflow-y-auto pr-2">
          <div className="space-y-3">
            {comments.map((comment) => (
              <div key={comment.id} className="flex gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={comment.authorImage} />
                  <AvatarFallback>
                    {comment.authorName.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="text-sm font-medium text-slate-700">
                      {comment.authorName}
                    </h4>
                    <span className="text-xs text-slate-500">
                      {new Date(comment.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-sm text-slate-600">{comment.content}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <Textarea
            value={comment}
            onKeyDown={async (e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                await onCreateComment();
              }
            }}
            onChange={(e) => setComment(e.target.value)}
            className="w-full rounded-md border px-3 py-2"
            rows={3}
            placeholder="Add a comment..."
          />
          <Button onClick={() => onCreateComment()} className="mt-2" size="sm">
            Post Comment
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
