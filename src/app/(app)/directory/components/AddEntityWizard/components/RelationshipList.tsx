'use client';

// src/components/entities/AddEntityWizard/components/RelationshipList.tsx

import React from 'react';
import { X, User, Building2, Users } from 'lucide-react';
import type { Relationship } from '../AddEntityWizard.types';
import type { Id } from '@/../convex/_generated/dataModel';

interface RelationshipListProps {
  relationships: Relationship[];
  onRemove: (id: Id<"entities"> | string) => void;
  emptyMessage?: string;
}

export const RelationshipList: React.FC<RelationshipListProps> = ({
  relationships,
  onRemove,
  emptyMessage = "No connections yet"
}) => {
  if (relationships.length === 0) {
    return (
      <div className="text-center py-12 text-gray-400">
        <Users size={48} className="mx-auto mb-3 opacity-50" />
        <p>{emptyMessage}</p>
        <p className="text-sm mt-1">You can always add these later</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {relationships.map((rel) => (
        <div 
          key={rel.id} 
          className="p-4 bg-gray-50 rounded-lg flex items-center justify-between hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center gap-3">
            <div className={`
              w-10 h-10 rounded-lg flex items-center justify-center
              ${rel.entity.type === 'person' ? 'bg-blue-100 text-blue-600' : 'bg-purple-100 text-purple-600'}
            `}>
              {rel.entity.type === 'person' ? <User size={20} /> : <Building2 size={20} />}
            </div>
            <div>
              <div className="font-medium">{rel.entity.name}</div>
              <div className="text-sm text-gray-500 capitalize">
                {rel.relationshipType.replace(/_/g, ' ')}
              </div>
            </div>
          </div>
          <button 
            onClick={() => onRemove(rel.id)}
            className="text-red-500 hover:text-red-700 transition-colors"
            aria-label={`Remove ${rel.entity.name}`}
          >
            <X size={20} />
          </button>
        </div>
      ))}
    </div>
  );
};