"use client";

import React from "react";
import { useState } from "react";
import {
  Building2,
  Zap,
  Wifi,
  Lock,
  Search,
  Settings,
  User,
  SquareTerminal,
  Home,
} from "lucide-react";
import <PERSON> from "next/link";
import AnimatedHomeIcon from "../ui/animated-home-icon";
import { DirectoryIcon } from "../ui/directory-icon";

interface ToolbarItem {
  icon: React.ReactNode;
  label: string;
  href: string;
  onClick?: () => void;
}

const toolbarItems: ToolbarItem[] = [
  {
    icon: <Home size={20} />,
    label: "Home",
    href: "/home",
  },
  {
    icon: <SquareTerminal size={20} />,
    label: "Tasks",
    href: "/tasks",
  },
  {
    icon: <DirectoryIcon size={20} />,
    label: "Directory",
    href: "/directory",
  },
  {
    icon: <Zap size={20} />,
    label: "Energy",
    href: "#",
  },
  {
    icon: <Wifi size={20} />,
    label: "Network",
    href: "#",
  },
  {
    icon: <Lock size={20} />,
    label: "Security",
    href: "#",
  },
  {
    icon: <Search size={20} />,
    label: "Search",
    href: "#",
  },
  {
    icon: <Settings size={20} />,
    label: "Settings",
    href: "#",
  },
  {
    icon: <User size={20} />,
    label: "Profile",
    href: "#",
  },
];

export default function MainNav() {
  const [isNavHovered, setIsNavHovered] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);

  return (
    <div className="fixed top-1/2 left-2 z-50 -translate-y-1/2">
      <nav
        className={`nav-glass-border transition-all duration-300 ease-out ${isNavHovered ? "w-48" : "w-14"} p-2`}
        onMouseEnter={() => setIsNavHovered(true)}
        onMouseLeave={() => setIsNavHovered(false)}
      >
        <ul className="relative space-y-1">
          {toolbarItems.map((item, index) => (
            <li
              key={index}
              onMouseEnter={() => setHoveredItem(index)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <Link
                href={item.href}
                onClick={item.onClick}
                className="flex items-center justify-start p-2.5 text-slate-400 transition-all duration-200 hover:text-slate-400 hover:bg-white/20 hover:backdrop-saturate-200 hover:scale-105 rounded-lg"
              >
                {/* Icon - always in the same position */}
                <span className="flex h-5 w-5 flex-shrink-0 items-center justify-center">
                  {item.label === "Home" ? (
                    <AnimatedHomeIcon size={20} isHovered={hoveredItem === index} />
                  ) : (
                    item.icon
                  )}
                </span>

                {/* Label - slides in from the right */}
                <span
                  className={`ml-3 text-sm font-medium whitespace-nowrap transition-all duration-300 ease-out ${isNavHovered ? "translate-x-0 opacity-100" : "translate-x-2 opacity-0"} `}
                >
                  {item.label}
                </span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
