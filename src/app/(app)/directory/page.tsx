"use client";

import { useMemo, useState } from "react";
import { api } from "@/../convex/_generated/api";
import { useConvexAuth, useQuery, useMutation } from "convex/react";
import { ConvexError } from "convex/values";
import { LoadingScreen } from "@/components/AppScreens/LoadingScreen";
import DirectoryCardLarge from "./components/DirectoryCardLarge";
import { AddEntityWizard } from "./components/AddEntityWizard";
import type { EntityFormData, Relationship } from "./components/AddEntityWizard/AddEntityWizard.types";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Users, Building2, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export default function DirectoryPage() {
  const { isLoading, isAuthenticated } = useConvexAuth();
  const router = useRouter();
  const [showWizard, setShowWizard] = useState(false);
  
  const entities = useQuery(
    api.entities.entitiesQueries.getAllEntities,
    isAuthenticated ? {} : "skip",
  );
  
  const createEntityFromWizard = useMutation(api.entities.entitiesMutations.createEntityFromWizard);

  const handleWizardComplete = async (wizardData: {
    entityType: "person" | "organization";
    formData: EntityFormData;
    relationships: Relationship[];
    displayName: string;
  }) => {
    try {
      let entityId;
      
      // Clean up form data to match Convex validation requirements
      const cleanedFormData = {
        ...wizardData.formData,
        person_category: wizardData.formData.person_category === '' ? undefined : wizardData.formData.person_category,
        organization_category: wizardData.formData.organization_category === '' ? undefined : wizardData.formData.organization_category,
      };
      
      if (wizardData.entityType === "person") {
        entityId = await createEntityFromWizard({
          entityType: "person" as const,
          formData: cleanedFormData,
          relationships: wizardData.relationships,
          displayName: wizardData.displayName,
        });
      } else {
        entityId = await createEntityFromWizard({
          entityType: "organization" as const,
          formData: cleanedFormData,
          relationships: wizardData.relationships,
          displayName: wizardData.displayName,
        });
      }
      setShowWizard(false);
      
      // Stay on directory page to show the newly created entity in the list
      // No redirect needed - the directory will refresh and show the new entity
    } catch (error: unknown) {
      console.error('Error creating entity:', error);
      
      const errorMessage = error instanceof ConvexError
        ? (error.data as string) || 'Failed to create contact'
        : 'Unexpected error occurred';
      
      toast.error(errorMessage);
    }
  };

  // Transform entity data and calculate counts in a single pass for optimal performance
  const { transformedEntities, peopleCount, orgCount } = useMemo(() => {
    const transformed = entities?.map((entity) => {
      // Extract website from entity_links for organizations
      const website = entity.entity_links?.find(link => link.type === "domain_name")?.url;
      
      return {
        ...entity,
        // Add computed fields that the DirectoryCardLarge component expects
        website,
        // Leave other optional fields undefined - component handles this gracefully
        // title, company, familyMemberCount, etc. can be added later
      };
    }) ?? [];
    
    // Calculate counts during transformation to avoid separate filter operations
    const peopleCount = transformed.filter((e) => e.entity_type === "person").length;
    const orgCount = transformed.filter((e) => e.entity_type === "organization").length;
    
    return { transformedEntities: transformed, peopleCount, orgCount };
  }, [entities]);

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">Directory</h1>
            <p className="text-slate-400">
              Your complete CRM directory of people and organizations
            </p>
          </div>
          <Button 
            onClick={() => setShowWizard(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus size={16} className="mr-2" />
            Add Contact
          </Button>
        </div>
        
        {/* Stats */}
        <div className="flex items-center gap-6 mt-4">
          <div className="flex items-center gap-2 text-sm text-slate-300">
            <Users size={16} />
            <span>{peopleCount} People</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-slate-300">
            <Building2 size={16} />
            <span>{orgCount} Organizations</span>
          </div>
          <div className="text-xs text-slate-500">
            Sorted by most recent
          </div>
        </div>
      </div>

      {/* Loading State */}
      {!entities && (
        <div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6"
          role="status"
          aria-label="Loading directory entries"
        >
          {Array.from({ length: 8 }, (_, index) => (
            <div key={index} className="w-full max-w-[270px] h-[330px]" aria-hidden="true">
              <Skeleton className="w-full h-full rounded-2xl" />
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {entities && entities.length === 0 && (
        <div className="text-center py-16">
          <div className="mb-4">
            <Users size={48} className="mx-auto text-slate-600" />
          </div>
          <h3 className="text-xl font-semibold text-slate-300 mb-2">
            No entities found
          </h3>
          <p className="text-slate-500">
            Start by adding people and organizations to your directory.
          </p>
        </div>
      )}

      {/* Entity Grid */}
      {entities && entities.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
          {transformedEntities.map((entity) => (
            <DirectoryCardLarge
              key={entity._id}
              variant={
                entity.entity_type === "organization" 
                  ? "organization" 
                  : "professional"
              }
              item={entity}
            />
          ))}
        </div>
      )}

      {/* Add Entity Wizard */}
      {showWizard && (
        <AddEntityWizard
          onClose={() => setShowWizard(false)}
          onComplete={handleWizardComplete}
          initialEntityType="person"
        />
      )}
    </div>
  );
}
