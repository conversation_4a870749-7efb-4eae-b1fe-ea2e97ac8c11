import { useEffect, useState, useCallback } from "react";
import type { DropResult } from "@hello-pangea/dnd";
import type { BoardData, Status, Priority } from "../types";
import type { Id } from "convex/_generated/dataModel";

export function useBoardData({
  boardData,
  changeTaskStatus,
}: {
  boardData: BoardData | undefined;
  changeTaskStatus: (args: {
    taskId: string;
    status?: Status;
    priority?: Priority;
    assigneeId?: string;
    title?: string;
    description?: string;
  }) => Promise<null>;
}) {
  const [localBoardData, setLocalBoardData] = useState(boardData);

  // Sync local state with backend data when it changes
  useEffect(() => {
    setLocalBoardData(boardData);
  }, [boardData]);

  const onDragEnd = useCallback(
    async (result: DropResult) => {
      if (!localBoardData) return;
      const { destination, source, draggableId } = result;

      // If there's no destination or if the item was dropped back in its original position
      if (
        !destination ||
        (destination.droppableId === source.droppableId &&
          destination.index === source.index)
      ) {
        return;
      }

      const sourceColumn = localBoardData.columns.find(
        (col) => col.name === source.droppableId,
      );
      const destColumn = localBoardData.columns.find(
        (col) => col.name === destination.droppableId,
      );

      // If source or destination column doesn't exist, do nothing
      if (!sourceColumn || !destColumn) {
        return;
      }

      // If the task is moved within the same column
      if (sourceColumn.name === destColumn.name) {
        const newTaskIds = Array.from(sourceColumn.taskIds);
        newTaskIds.splice(source.index, 1);
        newTaskIds.splice(destination.index, 0, draggableId);

        const newColumn = {
          ...sourceColumn,
          taskIds: newTaskIds,
        };

        const newBoardData = {
          ...localBoardData,
          columns: localBoardData.columns.map((col) =>
            col.name === newColumn.name ? newColumn : col,
          ),
        };

        setLocalBoardData(newBoardData);
        return;
      }

      // If the task is moved to a different column
      const sourceTaskIds = Array.from(sourceColumn.taskIds);
      sourceTaskIds.splice(source.index, 1);
      const newSourceColumn = {
        ...sourceColumn,
        taskIds: sourceTaskIds,
      };

      const destTaskIds = Array.from(destColumn.taskIds);
      destTaskIds.splice(destination.index, 0, draggableId);
      const newDestColumn = {
        ...destColumn,
        taskIds: destTaskIds,
      };

      const newBoardData = {
        ...localBoardData,
        columns: localBoardData.columns.map((col) => {
          if (col.name === newSourceColumn.name) return newSourceColumn;
          if (col.name === newDestColumn.name) return newDestColumn;
          return col;
        }),
      };

      setLocalBoardData(newBoardData);

      // Call mutation to update the task status in the DB
      const movedTask = localBoardData.tasks.find(
        (task) => task.id === draggableId,
      );
      if (movedTask) {
        await changeTaskStatus({
          taskId: movedTask.id as Id<"tasks">,
          status: newDestColumn.name as Status,
        });
      }
    },
    [localBoardData, changeTaskStatus],
  );

  return { localBoardData, onDragEnd };
}
