import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import type { SearchResult } from '../AddEntityWizard.types';

interface QuickCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (entity: SearchResult) => void;
  initialName?: string;
}

export const QuickCreateModal: React.FC<QuickCreateModalProps> = ({
  isOpen,
  onClose,
  onCreate,
  initialName = ''
}) => {
  const [entityType, setEntityType] = useState<'person' | 'organization'>('person');
  
  // Person fields
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');

  // Organization fields
  const [orgName, setOrgName] = useState('');
  const [industry, setIndustry] = useState('');
  const [phone, setPhone] = useState('');

  useEffect(() => {
    if (initialName) {
      if (entityType === 'person') {
        const nameParts = initialName.split(' ');
        setFirstName(nameParts[0] ?? '');
        setLastName(nameParts.slice(1).join(' ') ?? '');
      } else {
        setOrgName(initialName);
      }
    }
  }, [initialName, entityType]);

  if (!isOpen) return null;

  const handleCreate = () => {
    let newEntity: SearchResult;

    if (entityType === 'person') {
      if (!firstName.trim()) return;
      newEntity = {
        id: `temp_${Date.now()}`,
        name: `${firstName} ${lastName}`.trim(),
        type: 'person',
        subtext: email ?? 'New person'
      };
    } else {
      if (!orgName.trim()) return;
      newEntity = {
        id: `temp_${Date.now()}`,
        name: orgName.trim(),
        type: 'organization',
        subtext: industry ?? 'New organization'
      };
    }

    onCreate(newEntity);
    onClose();
  };

  const renderPersonForm = () => (
    <>
      <div className="grid grid-cols-2 gap-4">
        <input
          type="text"
          value={firstName}
          onChange={(e) => setFirstName(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="First Name"
          aria-label="First Name"
          autoFocus
        />
        <input
          type="text"
          value={lastName}
          onChange={(e) => setLastName(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Last Name"
        />
      </div>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        placeholder="Email (optional)"
        aria-label="Email (optional)"
        pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
      />
    </>
  );

  const renderOrganizationForm = () => (
    <>
      <input
        type="text"
        value={orgName}
        onChange={(e) => setOrgName(e.target.value)}
        className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        placeholder="Organization Name"
        aria-label="Organization Name"
        autoFocus
      />
      <input
        type="text"
        value={industry}
        onChange={(e) => setIndustry(e.target.value)}
        className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        placeholder="Industry (optional)"
        aria-label="Industry (optional)"
      />
      <input
        type="tel"
        value={phone}
        onChange={(e) => setPhone(e.target.value)}
        className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        placeholder="Phone (optional)"
        aria-label="Phone (optional)"
      />
    </>
  );

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-800">Quick Create New Entity</h3>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <X size={18} />
        </button>
      </div>
      
      <div className="space-y-4">
        <div className="bg-white p-1 rounded-lg border border-gray-200 flex">
          <button
            onClick={() => setEntityType('person')}
            className={`flex-1 py-1.5 rounded-md text-sm font-medium transition-colors ${
              entityType === 'person' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-50'
            }`}
          >
            Person
          </button>
          <button
            onClick={() => setEntityType('organization')}
            className={`flex-1 py-1.5 rounded-md text-sm font-medium transition-colors ${
              entityType === 'organization' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-50'
            }`}
          >
            Organization
          </button>
        </div>

        {entityType === 'person' ? renderPersonForm() : renderOrganizationForm()}

        <button
          onClick={handleCreate}
          className="w-full bg-blue-600 text-white font-semibold py-2.5 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Add & Create Relationship
        </button>
      </div>
    </div>
  );
};
