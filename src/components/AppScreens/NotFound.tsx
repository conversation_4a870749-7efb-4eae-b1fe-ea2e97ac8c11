"use client";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, Home, Search, FileX } from "lucide-react";

interface NotFoundProps {
  title?: string;
  description?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  showContactSupport?: boolean;
  className?: string;
  icon?: "search" | "file" | "custom";
  customIcon?: React.ReactNode;
  backUrl?: string;
}

export default function NotFoundComponent({
  title = "Page Not Found",
  description = "Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.",
  showBackButton = true,
  showHomeButton = true,
  showContactSupport = true,
  className = "",
  icon = "search",
  customIcon,
  backUrl,
}: NotFoundProps) {
  const router = useRouter();

  const handleGoBack = () => {
    if (backUrl) {
      router.push(backUrl);
    } else if (typeof window !== "undefined" && window.history.length > 1) {
      router.back();
    } else {
      router.push("/");
    }
  };

  const getIcon = () => {
    if (customIcon) return customIcon;

    switch (icon) {
      case "file":
        return <FileX className="text-muted-foreground h-10 w-10" />;
      case "search":
      default:
        return <Search className="text-muted-foreground h-10 w-10" />;
    }
  };

  return (
    <div
      className={`from-background to-muted/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4 ${className}`}
    >
      <div className="w-full max-w-md">
        <Card className="text-center">
          <CardHeader className="pb-4">
            <div className="bg-muted mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full">
              {getIcon()}
            </div>
            <CardTitle className="text-2xl font-bold">{title}</CardTitle>
            <CardDescription className="text-base">
              {description}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-muted-foreground/30 text-6xl font-bold">
              404
            </div>
            {(showHomeButton || showBackButton) && (
              <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
                {showHomeButton && (
                  <Button
                    asChild
                    variant="default"
                    className="w-full sm:w-auto"
                  >
                    <Link href="/">
                      <Home className="mr-2 h-4 w-4" />
                      Go Home
                    </Link>
                  </Button>
                )}
                {showBackButton && (
                  <Button
                    onClick={handleGoBack}
                    variant="outline"
                    className="w-full sm:w-auto"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Go Back
                  </Button>
                )}
              </div>
            )}
            {showContactSupport && (
              <p className="text-muted-foreground text-sm">
                If you believe this is an error, please{" "}
                <Link href="/contact" className="text-primary hover:underline">
                  contact support
                </Link>
                .
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
