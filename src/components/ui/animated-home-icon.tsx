"use client";

import React from "react";
import { motion } from "motion/react";

interface AnimatedHomeIconProps {
  size?: number;
  className?: string;
  isHovered: boolean;
}

const AnimatedHomeIcon: React.FC<AnimatedHomeIconProps> = ({
  size = 24,
  className,
  isHovered,
}) => {
  const variants = {
    hover: {
      // Use rotateY for a 3D swing effect
      rotateY: -70,
    },
    initial: {
      rotateY: 0,
    },
  };

  const svgVariants = {
    hover: {
      scale: 1.1, // Slightly increase scale for more pop
    },
    initial: {
      scale: 1,
    },
  };

  return (
    // Add a wrapper div to set the 3D perspective
    <motion.div style={{ perspective: "800px" }}>
      <motion.svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className={className}
        variants={svgVariants}
        animate={isHovered ? "hover" : "initial"}
        transition={{ type: "spring", stiffness: 400, damping: 30 }}
      >
        {/* House Frame */}
        <path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
        {/* Door */}
        <motion.path
          d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"
          // Set the origin to the left hinge for a realistic swing
          style={{ transformOrigin: "10px 12px" }}
          variants={variants}
          animate={isHovered ? "hover" : "initial"}
          transition={{ type: "spring", stiffness: 400, damping: 30 }}
        />
      </motion.svg>
    </motion.div>
  );
};

export default AnimatedHomeIcon;
