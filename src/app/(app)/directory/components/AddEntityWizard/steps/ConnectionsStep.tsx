'use client';
import React, { useState } from 'react';
import { Plus, User, Building2 } from 'lucide-react';
import { RelationshipSearch } from '../components/RelationshipSearch';
import { RelationshipList } from '../components/RelationshipList';
import { QuickCreateModal } from '../components/QuickCreateModal';
import { useEntitySearch } from '../hooks/useEntitySearch';
import { SUGGESTED_RELATIONSHIPS } from '../AddEntityWizard.constants';
import type { ConnectionsStepProps, SearchResult } from '../AddEntityWizard.types';

export const ConnectionsStep: React.FC<ConnectionsStepProps> = ({ 
  formData, 
  entityType,
  entityCategory,
  relationships,
  onAddRelationship,
  onRemoveRelationship
}) => {
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [quickCreateOpen, setQuickCreateOpen] = useState(false);
  
  const excludeIds = relationships.map(rel => rel.entity.id);
  const { searchQuery, setSearchQuery, searchResults, isSearching, clearSearch } = useEntitySearch({ excludeIds });

  const suggestions = SUGGESTED_RELATIONSHIPS[entityCategory] ?? [];
  const displayName = entityType === 'person' 
    ? formData.person_first_name || 'this person'
    : formData.organization_name || 'this organization';

  const handleSelectResult = (result: SearchResult) => {
    onAddRelationship(result);
    clearSearch();
    setShowSearchResults(false);
  };

  const handleQuickCreate = (entity: SearchResult) => {
    onAddRelationship(entity);
    clearSearch();
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Connect the dots</h2>
        <p className="text-gray-600">
          How does {displayName} relate to other people and organizations?
        </p>
      </div>

      <div className="max-w-2xl mx-auto">
        {/* Smart suggestions based on category */}
        {suggestions.length > 0 && relationships.length === 0 && (
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
            <h3 className="font-medium text-gray-900 mb-3">Suggested connections</h3>
            <div className="space-y-2">
              {suggestions.map((suggestion, idx) => (
                <button
                  key={idx}
                  className="w-full text-left p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
                  onClick={() => {
                    setSearchQuery('');
                    setShowSearchResults(true);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {suggestion.type === 'organization' ? 
                        <Building2 size={20} className="text-purple-600" /> : 
                        <User size={20} className="text-blue-600" />
                      }
                      <span className="text-sm">{suggestion.suggestion}</span>
                    </div>
                    <Plus size={16} className="text-gray-400" />
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Search interface */}
        <RelationshipSearch
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          searchResults={searchResults}
          isSearching={isSearching}
          onSelectResult={handleSelectResult}
          onCreateNew={() => setQuickCreateOpen(true)}
          showResults={showSearchResults}
          onShowResultsChange={setShowSearchResults}
        />

        {/* Active relationships */}
        <RelationshipList
          relationships={relationships}
          onRemove={onRemoveRelationship}
          emptyMessage={`No connections added yet for ${displayName}`}
        />

        {/* Quick Create Modal */}
        <QuickCreateModal
          isOpen={quickCreateOpen}
          onClose={() => {
            setQuickCreateOpen(false);
            clearSearch();
          }}
          onCreate={handleQuickCreate}
          initialName={searchQuery}
        />
      </div>
    </div>
  );
};