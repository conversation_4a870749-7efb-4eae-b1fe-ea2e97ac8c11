Convex Data-Access Cheat Sheet

⸻

1. Read a Single Document

export const getTask = query({
  args: { taskId: v.id("tasks") },
  handler: async (ctx, { taskId }) => ctx.db.get(taskId),
});

Always validate IDs with v.id to avoid cross-table leaks.

⸻

2. Query Documents

Action	API	Notes
Pick table	ctx.db.query("tasks")	Starting point for every query
Filter efficiently	.withIndex("by_x", q => q.eq("field", val)… )	Must declare index first
Filter slow / small tables	.filter(q => …)	Table-scan; fine for prototyping
Order	.order("asc" | "desc")	Defaults to _creationTime, ascending
Retrieve	.collect() (all) · .take(n) · .first() · .unique()	Use take / first / unique to skip full scans


⸻

3. Indexes

Define

defineTable({ channel: v.id("channels"), … })
  .index("by_channel", ["channel"])
  .index("by_channel_user", ["channel", "user"]);

Query

ctx.db
  .query("messages")
  .withIndex("by_channel", q =>
    q.eq("channel", channel)
     .gt("_creationTime", start)
     .lt("_creationTime", end))
  .collect();

Rules
	1.	Chain .eq() from left-most index field.
	2.	At most one lower (gt/gte) and/or upper (lt/lte) bound after the equalities.
	3.	Omitting a range turns the query into a table scan unless you also use take / first / unique / paginate.

Ordering follows index column order; _creationTime is always the final tiebreaker.

Maintenance: first deploy after adding an index is slower (back-fill). Delete indexes only when unused—convex deploy will drop them.

⸻

4. Pagination

Write a paginated query

export const list = query({
  args: { paginationOpts: paginationOptsValidator },
  handler: (ctx, { paginationOpts }) =>
    ctx.db.query("messages").order("desc").paginate(paginationOpts),
});

React hook

const { results, status, loadMore } = usePaginatedQuery(
  api.messages.list,
  {},                       // extra args
  { initialNumItems: 5 }    // first page size
);

Statuses: "LoadingFirstPage" → "CanLoadMore" → "LoadingMore" → "Exhausted".

Manual loop

let cursor = null;
do {
  const { page, continueCursor, isDone } = await client.query(
    api.messages.list, { paginationOpts: { numItems: 5, cursor } });
  // use page…
  cursor = continueCursor;
} while (!isDone);

Pages resize reactively if rows are added/removed.

⸻

5. Performance Guidelines
	•	Use indexes for any query touching thousands of rows.
	•	Keep index ranges specific; otherwise add a new index.
	•	Pair withIndex without a range with take / first / unique / paginate.
	•	Limit to ≤ 32 indexes per table to avoid write overhead.

⸻

This condensed guide keeps all critical concepts, APIs, and best-practice warnings while stripping out illustrative anecdotes and redundant examples.