import type { Page } from '@playwright/test';

export async function navigateToAuthenticatedPage(page: Page, path: string = '/tasks') {
  // Navigate to a protected page - authentication state is already loaded
  await page.goto(path);
  
  // Wait for the page to load
  await page.waitForLoadState('networkidle');
  
  // Verify we successfully reached the protected page
  const currentUrl = page.url();
  if (currentUrl.includes('/login') || currentUrl.includes('/sign-in')) {
    throw new Error(`Authentication failed - redirected to login: ${currentUrl}`);
  }
  
  console.log('Authentication successful - on page:', currentUrl);
}

export async function setupAuthenticatedSession(page: Page) {
  // Authentication state is already loaded from stored state
  // Just navigate to the protected page
  await navigateToAuthenticatedPage(page);
}