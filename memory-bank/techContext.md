# Tech Context

## Technologies Used

### Frontend
- **Next.js:** React framework for building user interfaces.
- **React:** JavaScript library for building interactive UIs.
- **TypeScript:** Superset of JavaScript that adds static typing.
- **Tailwind CSS:** Utility-first CSS framework for rapid UI development.
- **Shadcn/ui:** Reusable UI components built with Radix UI and Tailwind CSS.

### Backend/Database
- **Convex:** Fullstack application platform providing a real-time database and serverless functions.

### Authentication
- **Clerk:** User management and authentication platform.

### Other
- **Zod:** TypeScript-first schema declaration and validation library.
- **React Hook Form:** Library for building forms with React hooks.
- **Lucide React:** Icon library.
- **Sonner:** Toast notification library.

## Development Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or pnpm (pnpm is preferred as per `pnpm-lock.yaml`)
- Git

### Installation Steps
1. Clone the repository: `git clone [repository-url]`
2. Navigate to the project directory: `cd caliber`
3. Install dependencies: `pnpm install`
4. Set up environment variables (e.g., Clerk keys, Convex deployment URL). Refer to `.env.local.example` if available.
5. Start the development server: `pnpm dev`

## Technical Constraints
- **Convex Schema:** All data models must be defined in `convex/schema.ts`.
- **Middleware for Auth Routing:** The Next.js middleware is the primary mechanism for handling authentication-based redirects and protecting routes.
- **Clerk as Source of Truth:** The application relies on Clerk's JWT as the single source of truth for user and organization identity.
- **Environment Variables:** Sensitive information and API keys must be stored in environment variables.

## Dependencies
Dependencies are managed via `package.json` and `pnpm-lock.yaml`. Key dependencies include:
- `next`
- `react`, `react-dom`
- `@clerk/nextjs`
- `convex`
- `tailwindcss`
- `class-variance-authority`
- `clsx`
- `lucide-react`
- `next-themes`
- `react-hook-form`
- `sonner`
- `zod`

## Tool Usage Patterns
- **Convex CLI:** Used for deploying Convex functions (`npx convex deploy`), generating types (`npx convex codegen`), and managing Convex projects.
- **Next.js CLI:** Used for running the development server (`next dev`) and building the application (`next build`).
- **pnpm:** Package manager for installing and managing dependencies.
