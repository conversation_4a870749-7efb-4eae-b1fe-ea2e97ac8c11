{"name": "Caliber App Header Testing with MCP", "version": "1.0.0", "mcpServers": {"playwright-microsoft": {"command": "npx", "args": ["@playwright/mcp"]}, "playwright-executeautomation": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}}, "testTargets": {"appHeader": {"component": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location": "src/components/AppLayout/AppHeader.tsx", "features": ["User avatar display", "Profile dropdown functionality", "Organization switcher", "Breadcrumb navigation", "Responsive design", "Clerk authentication integration"]}}, "environment": {"baseUrl": "http://localhost:3000", "authRequired": true, "testUser": {"username": "<EMAIL>", "password": "caliber-test"}}}