'use client';

// src/app/(app)/directory/components/AddEntityWizard/steps/OrganizationTypeStep.tsx

import React from 'react';
import { Check } from 'lucide-react';
import { ORGANIZATION_CATEGORIES } from '../AddEntityWizard.constants';
import type { OrganizationStepProps, OrganizationCategory } from '../AddEntityWizard.types';

export const OrganizationTypeStep: React.FC<OrganizationStepProps> = ({ 
  formData, 
  onUpdate,
  organizationCategory 
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">What type of organization?</h2>
        <p className="text-gray-600">This helps us understand the relationship</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {ORGANIZATION_CATEGORIES.map((cat) => {
          const isSelected = organizationCategory === cat.id;
          const buttonStyles = isSelected
            ? { borderColor: cat.color, backgroundColor: `${cat.color}1A` } // 10% opacity
            : {};
          const iconContainerStyles = isSelected
            ? { backgroundColor: `${cat.color}33`, color: cat.color } // 20% opacity
            : {};

          return (
            <button
              key={cat.id}
              onClick={() => onUpdate({ organization_category: cat.id as OrganizationCategory })}
              className={`
                relative p-6 rounded-xl border-2 transition-all text-left
                ${!isSelected ? 'border-gray-200 hover:border-gray-300 bg-white' : 'border-transparent'
                }
              `}
              style={buttonStyles}
            >
              <div className="flex items-start gap-4">
                <div
                  className={`
                    w-12 h-12 rounded-lg flex items-center justify-center
                    ${!isSelected ? 'bg-gray-100 text-gray-600' : ''}
                  `}
                  style={iconContainerStyles}
                >
                  <cat.icon size={24} />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{cat.label}</h3>
                </div>
                {isSelected && (
                  <div className="absolute top-4 right-4">
                    <Check size={20} style={{ color: cat.color }} />
                  </div>
                )}
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};