"use client";
import { TaskActions } from "./TaskActions";
import { TaskComments } from "./TaskComments";
import { TaskDescription } from "./TaskDescription";
import { TaskDetailsSidebar } from "./TaskDetailsSidebar";
import { TaskHeader } from "./TaskHeader";
import type { Preloaded } from "convex/react";
import type { api } from "convex/_generated/api";
import { usePreloadedQuery } from "convex/react";
import { LoadingScreen } from "@/components/AppScreens/LoadingScreen";

export const TaskDetails = (props: {
  preloadedTask: Preloaded<typeof api.tasks.tasksQueries.getTask>;
  preloadedComments: Preloaded<typeof api.comments.commentsQueries.getComments>;
}) => {
  const task = usePreloadedQuery(props.preloadedTask);
  const comments = usePreloadedQuery(props.preloadedComments);
  if (!task) return <LoadingScreen />;
  return (
    <div className="container mx-auto max-w-5xl bg-transparent py-6">
      <TaskHeader title={task.title} task={task} />
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <TaskDescription description={task.description} />
          <TaskComments comments={comments} taskId={task.id} />
        </div>
        <div>
          <TaskDetailsSidebar
            status={task.status}
            priority={task.priority}
            assignee={task.assignee}
          />
          <TaskActions taskId={task.id} />
        </div>
      </div>
    </div>
  );
};
