import { api } from "@/../convex/_generated/api";
import { preloadQuery } from "convex/nextjs";
import { TaskDetails } from "./components/TaskDetails";
import { redirect } from "next/navigation";

export default async function TaskDetailsPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  try {
    const preloadedTask = await preloadQuery(api.tasks.tasksQueries.getTask, {
      id,
    });

    if (!preloadedTask) {
      redirect("/tasks");
    }
    const preloadedComments = await preloadQuery(
      api.comments.commentsQueries.getComments,
      { parentId: id },
    );
    return (
      <TaskDetails
        preloadedTask={preloadedTask}
        preloadedComments={preloadedComments}
      />
    );
  } catch (error) {
    console.error(error);
    redirect("/tasks");
  }
}
