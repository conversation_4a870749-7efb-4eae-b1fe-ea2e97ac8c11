import { internalMutation, mutation } from "../_generated/server";
import { v, ConvexError } from "convex/values";
import type { UserJSON, OrganizationMembershipJSON } from "@clerk/nextjs/server";

const clerkUserValidator = v.object({
  id: v.string(),
  first_name: v.union(v.string(), v.null()),
  last_name: v.union(v.string(), v.null()),
  primary_email_address_id: v.union(v.string(), v.null()),
  email_addresses: v.array(
    v.object({
      id: v.string(),
      email_address: v.string(),
    })
  ),
  image_url: v.string(),
});

export const upsertUserProfile = internalMutation({
  args: { clerkUser: clerkUserValidator },
  returns: v.null(),
  handler: async (ctx, { clerkUser }) => {
    const userRecord = await ctx.db
      .query("userProfiles")
      .withIndex("by_clerkUserId", (q) => q.eq("clerkUserId", clerkUser.id))
      .unique();

    const userEmail = clerkUser.email_addresses.find(
      (e) => e.id === clerkUser.primary_email_address_id
    )?.email_address;

    if (!userEmail || !clerkUser.first_name || !clerkUser.last_name) {
      throw new ConvexError("Missing required user data from Clerk");
    }

    if (userRecord === null) {
      await ctx.db.insert("userProfiles", {
        clerkUserId: clerkUser.id,
        email: userEmail,
        firstName: clerkUser.first_name,
        lastName: clerkUser.last_name,
        imageUrl: clerkUser.image_url,
      });
    } else {
      await ctx.db.patch(userRecord._id, {
        email: userEmail,
        firstName: clerkUser.first_name,
        lastName: clerkUser.last_name,
        imageUrl: clerkUser.image_url,
      });
    }
  },
});

export const deleteUserProfile = internalMutation({
  args: { clerkUserId: v.string() },
  returns: v.null(),
  handler: async (ctx, { clerkUserId }) => {
    const userProfile = await ctx.db
      .query("userProfiles")
      .withIndex("by_clerkUserId", (q) => q.eq("clerkUserId", clerkUserId))
      .unique();

    if (userProfile === null) {
      // It's possible we can receive a delete webhook for a user that never fully existed
      return;
    }

    // First, delete all tenant memberships for this user
    const memberships = await ctx.db
      .query("tenantMemberships")
      .withIndex("by_user_org", (q) => q.eq("clerkUserId", clerkUserId))
      .collect();

    await Promise.all(memberships.map((m) => ctx.db.delete(m._id)));

    // Finally, delete the user profile
    await ctx.db.delete(userProfile._id);
  },
});

export const upsertTenantMembership = internalMutation({
  args: { clerkMembership: v.any() },
  returns: v.union(v.id("tenantMemberships"), v.null()),
  handler: async (
    ctx,
    { clerkMembership }: { clerkMembership: OrganizationMembershipJSON }
  ) => {
    const membership = await ctx.db
      .query("tenantMemberships")
      .withIndex("by_org_user", (q) =>
        q
          .eq("clerkTenantId", clerkMembership.organization.id)
          .eq("clerkUserId", clerkMembership.public_user_data.user_id)
      )
      .unique();

    if (membership === null) {
      return await ctx.db.insert("tenantMemberships", {
        clerkTenantId: clerkMembership.organization.id,
        clerkUserId: clerkMembership.public_user_data.user_id,
      });
    }
    // Note: We are not handling role updates for now, as the schema doesn't support it.
  },
});

export const deleteTenantMembership = internalMutation({
  args: { clerkMembership: v.any() },
  returns: v.null(),
  handler: async (
    ctx,
    { clerkMembership }: { clerkMembership: OrganizationMembershipJSON }
  ) => {
    const membership = await ctx.db
      .query("tenantMemberships")
      .withIndex("by_org_user", (q) =>
        q
          .eq("clerkTenantId", clerkMembership.organization.id)
          .eq("clerkUserId", clerkMembership.public_user_data.user_id)
      )
      .unique();

    if (membership === null) {
      // This can happen if webhooks arrive out of order. It's safe to ignore.
      return;
    }

    await ctx.db.delete(membership._id);
  },
});
