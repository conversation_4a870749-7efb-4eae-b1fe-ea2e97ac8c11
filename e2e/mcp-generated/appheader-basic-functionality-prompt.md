Generate comprehensive Playwright tests for an AppHeader component with the following requirements:
    
    Component: AppHeader
    Location: src/components/AppLayout/AppHeader.tsx
    Base URL: http://localhost:3000
    
    Test Features:
    - User avatar display
    - Profile dropdown functionality
    - Organization switcher
    - Breadcrumb navigation
    - Responsive design
    - Clerk authentication integration
    
    Requirements:
    1. Use Clerk authentication with test user credentials
    2. Handle sidebar visibility (may hide avatar button)
    3. Test responsive design across viewport sizes
    4. Verify accessibility compliance
    5. Include proper error handling and assertions
    
    Authentication Details:
    - Username: <EMAIL>
    - Password: caliber-test
    - Uses Clerk authentication system
    
    Generate modern, robust Playwright tests with proper selectors and assertions.