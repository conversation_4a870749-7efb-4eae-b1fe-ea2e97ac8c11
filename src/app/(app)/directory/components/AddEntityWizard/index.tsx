// src/app/(app)/directory/components/AddEntityWizard/index.tsx

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { WizardHeader } from './components/WizardHeader';
import { WizardProgress } from './components/WizardProgress';
import { WizardNavigation } from './components/WizardNavigation';
import { PersonCategoryStep } from './steps/PersonCategoryStep';
import { PersonInfoStep } from './steps/PersonInfoStep';
import { OrganizationTypeStep } from './steps/OrganizationTypeStep';
import { OrganizationInfoStep } from './steps/OrganizationInfoStep';
import { ClientTypeStep } from './steps/ClientTypeStep';
import { ConnectionsStep } from './steps/ConnectionsStep';
import { useEntityForm } from './hooks/useEntityForm';
import { useRelationships } from './hooks/useRelationships';
import { WIZARD_STEPS } from './AddEntityWizard.constants';
import { User } from 'lucide-react';
import type { EntityType, EntityFormData, Relationship, WizardStep } from './AddEntityWizard.types';

interface AddEntityWizardProps {
  onClose: () => void;
  onComplete: (data: {
    entityType: EntityType;
    formData: EntityFormData;
    relationships: Relationship[];
    displayName: string;
  }) => void;
  initialEntityType?: EntityType;
}

export const AddEntityWizard: React.FC<AddEntityWizardProps> = ({
  onClose,
  onComplete,
  initialEntityType = 'person'
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [entityType, setEntityType] = useState<EntityType>(initialEntityType);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { formData, errors, updateFormData, validateStep, resetForm, getDisplayName } = useEntityForm();
  const { relationships, addRelationship, removeRelationship, clearRelationships } = useRelationships();

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const { person_category, client_type } = formData;

  const steps: WizardStep[] = useMemo(() => {
    const isClient = person_category === 'client' || person_category === 'potential_client';

    if (!isClient) {
      return WIZARD_STEPS[entityType];
    }

    const newSteps: (WizardStep | undefined)[] = [
      WIZARD_STEPS.person[0],
      { number: 2, title: 'Client Type', icon: User }
    ];

    if (client_type === 'individual') {
      newSteps.push(WIZARD_STEPS.person[1]);
      newSteps.push(WIZARD_STEPS.person[2]);
    } else if (client_type === 'organization') {
      // For client organizations, skip the organization type step since we know it's a client
      newSteps.push(WIZARD_STEPS.organization[1]); // Basic Info
      newSteps.push(WIZARD_STEPS.organization[2]); // Connections
    }
    
    return newSteps
      .filter((step): step is WizardStep => !!step)
      .map((step, index) => ({ ...step, number: index + 1 }));
  }, [entityType, person_category, client_type]);

  const progressSteps: WizardStep[] = useMemo(() => {
    const isClient = person_category === 'client' || person_category === 'potential_client';

    if (isClient) {
      const allSteps = [
        WIZARD_STEPS.person[0],
        { number: 2, title: 'Client Type', icon: User },
      ];
      
      // Add appropriate steps based on client type selection
      if (client_type === 'individual') {
        allSteps.push(WIZARD_STEPS.person[1], WIZARD_STEPS.person[2]);
      } else if (client_type === 'organization') {
        allSteps.push(WIZARD_STEPS.organization[1], WIZARD_STEPS.organization[2]);
      } else {
        // Default to showing both options until client type is selected
        allSteps.push(WIZARD_STEPS.person[1], WIZARD_STEPS.person[2]);
      }
      
      return allSteps
        .filter((step): step is WizardStep => !!step)
        .map((step, index) => ({ ...step, number: index + 1 }));
    }
    
    return WIZARD_STEPS[entityType];
  }, [entityType, person_category, client_type]);

  const canProceed = useMemo(() => {
    const stepInfo = steps[currentStep - 1];
    if (!stepInfo) return false;

    switch (stepInfo.title) {
      case 'Who is this?':
        return !!person_category;
      case 'Client Type':
        return !!client_type;
      case 'Basic Info':
        if (entityType === 'organization' || formData.client_type === 'organization' || formData.person_category === 'business_contact') {
          return !!formData.organization_name.trim();
        }
        return !!formData.person_first_name.trim();
      case 'Connections':
        return true;
      default:
        return false;
    }
  }, [currentStep, steps, person_category, client_type, entityType, formData]);

  const handleNext = useCallback(() => {
    // Handle business_contact (Organization) selection from main flow
    if (formData.person_category === 'business_contact' && currentStep === 1) {
      setEntityType('organization');
      updateFormData({
        organization_category: 'service_provider', // Default for business contacts
        person_first_name: '',
        person_last_name: '',
        person_birthday: '',
        person_notes: '',
      });
    }

    const isClient = formData.person_category === 'client' || formData.person_category === 'potential_client';
    if (isClient && currentStep === 2) { // After ClientTypeStep
      if (formData.client_type === 'individual') {
        setEntityType('person');
        updateFormData({
          organization_name: '',
          organization_category: '',
          organization_industry: '',
          organization_employees_amount: '',
          organization_annual_revenue: '',
          organization_notes: '',
        });
      } else if (formData.client_type === 'organization') {
        setEntityType('organization');
        updateFormData({
          person_first_name: '',
          person_last_name: '',
          person_birthday: '',
          person_notes: '',
          organization_category: 'client',
        });
      }
    }

    const stepInfo = steps[currentStep - 1];
    if (stepInfo && stepInfo.title === 'Client Type') {
      if (canProceed) {
        setCurrentStep(currentStep + 1);
      }
      return;
    }

    if (validateStep(currentStep, entityType) && currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, steps, validateStep, entityType, formData.person_category, formData.client_type, canProceed, updateFormData]);

  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    
    try {
      const finalFormData = { ...formData };
      if (entityType === 'person') {
        delete (finalFormData as Partial<EntityFormData>).organization_name;
        delete (finalFormData as Partial<EntityFormData>).organization_category;
        delete (finalFormData as Partial<EntityFormData>).organization_industry;
        delete (finalFormData as Partial<EntityFormData>).organization_employees_amount;
        delete (finalFormData as Partial<EntityFormData>).organization_annual_revenue;
        delete (finalFormData as Partial<EntityFormData>).organization_notes;
      } else {
        delete (finalFormData as Partial<EntityFormData>).person_first_name;
        delete (finalFormData as Partial<EntityFormData>).person_last_name;
        delete (finalFormData as Partial<EntityFormData>).person_category;
        delete (finalFormData as Partial<EntityFormData>).person_birthday;
        delete (finalFormData as Partial<EntityFormData>).person_notes;
      }

      // Prepare data for submission
      const submitData = {
        entityType,
        formData: finalFormData,
        relationships,
        displayName: getDisplayName()
      };
      
      // Call onComplete with the data
      onComplete(submitData);
      
      // Reset form and close wizard
      resetForm();
      clearRelationships();
      onClose();
    } catch (error) {
      console.error('Error submitting entity:', error);
      
      // In a real app, you'd show a proper error notification
      // For now, we'll let the parent component handle the error
      throw error; // Re-throw to let parent handle it
    } finally {
      setIsSubmitting(false);
    }
  }, [entityType, formData, relationships, getDisplayName, onComplete, resetForm, clearRelationships, onClose]);

  const renderStepContent = () => {
    const commonProps = {
      formData,
      onUpdate: updateFormData,
      onNext: handleNext,
      onBack: handleBack,
    };

    const stepInfo = steps[currentStep - 1];
    if (!stepInfo) return null;

    switch (stepInfo.title) {
      case 'Who is this?':
        return <PersonCategoryStep {...commonProps} personCategory={formData.person_category} />;
      case 'Client Type':
        return <ClientTypeStep {...commonProps} />;
      case 'Basic Info':
        // If we're in the client flow and selected organization type, or if entityType is organization, or if business_contact is selected
        if (entityType === 'organization' || formData.client_type === 'organization' || formData.person_category === 'business_contact') {
          return <OrganizationInfoStep {...commonProps} organizationCategory={formData.organization_category} />;
        }
        return <PersonInfoStep {...commonProps} personCategory={formData.person_category} />;
      case 'Connections':
        return (
          <ConnectionsStep
            {...commonProps}
            entityType={entityType}
            entityCategory={formData.person_category || formData.organization_category}
            relationships={relationships}
            onAddRelationship={addRelationship}
            onRemoveRelationship={removeRelationship}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-start pl-4 pr-80">
      {/* Modal backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal content */}
      <div 
        className="relative z-10 w-full max-w-4xl max-h-[90vh] ml-8 bg-white rounded-2xl shadow-2xl overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <WizardHeader 
          title="Add New Contact" 
          onClose={onClose} 
        />
        
        <div className="flex flex-col max-h-[calc(90vh-4rem)]">
          <WizardProgress 
            steps={progressSteps} 
            currentStep={currentStep} 
          />

          <div className="flex-1 overflow-y-auto px-6 py-2">
            <div className="bg-gray-50 rounded-xl p-4 h-[65vh] overflow-y-auto">
              {renderStepContent()}
            </div>
          </div>

          <div className="border-t bg-white px-6 py-2">
            <WizardNavigation
              currentStep={currentStep}
              totalSteps={steps.length}
              canProceed={canProceed}
              onBack={handleBack}
              onNext={handleNext}
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Export for easy access
export default AddEntityWizard;
