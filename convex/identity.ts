import { query } from "./_generated/server";
import { ConvexError, v } from "convex/values";

export const getCurrentUser = query({
  args: {},
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id("userProfiles"),
      _creationTime: v.number(),
      clerkUserId: v.string(),
      firstName: v.string(),
      lastName: v.string(),
      email: v.string(),
      imageUrl: v.optional(v.string()),
    }),
  ),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Unauthorized");
    }
    const user = await ctx.db
      .query("userProfiles")
      .withIndex("by_clerkUserId", (q) =>
        q.eq("clerkUserId", identity.subject),
      )
      .first();
    if (!user) {
      throw new ConvexError("User not found");
    }
    return user;
  },
});
