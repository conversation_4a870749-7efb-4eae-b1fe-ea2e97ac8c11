// src/components/entities/AddEntityWizard/hooks/useRelationships.ts

import { useState, useCallback } from 'react';
import type { Relationship, SearchResult } from '../AddEntityWizard.types';
import type { Id } from '@/../convex/_generated/dataModel';

export const useRelationships = () => {
  const [relationships, setRelationships] = useState<Relationship[]>([]);

  const addRelationship = useCallback((entity: SearchResult, relationshipType?: string) => {
    setRelationships(prev => {
      // A relationship is uniquely identified by the entity it points to
      if (prev.some(r => r.id === entity.id)) return prev;
      
      const newRelationship: Relationship = {
        id: entity.id, // Use the entity's ID as the relationship's ID
        entity,
        relationshipType: relationshipType ?? inferRelationshipType(entity)
      };
      
      return [...prev, newRelationship];
    });
  }, []);

  const removeRelationship = useCallback((id: Id<"entities"> | string) => {
    setRelationships(prev => prev.filter(rel => rel.id !== id));
  }, []);

  const updateRelationshipType = useCallback((id: Id<"entities"> | string, newType: string) => {
    setRelationships(prev => 
      prev.map(rel => 
        rel.id === id ? { ...rel, relationshipType: newType } : rel
      )
    );
  }, []);

  const clearRelationships = useCallback(() => {
    setRelationships([]);
  }, []);

  const hasRelationship = useCallback((entityId: Id<"entities">): boolean => {
    return relationships.some(rel => rel.entity.id === entityId);
  }, [relationships]);

  return {
    relationships,
    addRelationship,
    removeRelationship,
    updateRelationshipType,
    clearRelationships,
    hasRelationship
  };
};

// Helper function to infer relationship type based on entity
const inferRelationshipType = (entity: SearchResult): string => {
  // This would be more sophisticated in a real app
  if (entity.type === 'organization') {
    return 'works_at';
  }
  return 'related_to';
};