---
description: 
globs: 
alwaysApply: false
---
# Comprehensive Coding Guidelines Checklist

## Architecture & Design Patterns
- [ ] **Single Responsibility**: Each function/component should do one thing well
- [ ] **DRY (Don't Repeat Yourself)**: Extract common logic into reusable functions/components
- [ ] **KISS (Keep It Simple)**: Prefer simple solutions over clever ones
- [ ] **Guard clauses vs if-then**: Use early returns (guards) for preconditions, if-then for business logic
- [ ] **Composition over inheritance**: Prefer composing behavior rather than extending classes
- [ ] **Dependency injection**: Pass dependencies as parameters rather than importing directly
- [ ] **Fail fast**: Validate inputs early and throw meaningful errors immediately
- [ ] **Immutability first**: Prefer const over let, avoid mutating objects/arrays directly

## State Management
- [ ] **Keep state close to component**: Only lift state when multiple components need it
- [ ] **Single source of truth**: Avoid duplicating state across components
- [ ] **Derived state**: Calculate values from existing state rather than storing separately
- [ ] **State colocation**: Keep related state together in objects/reducers
- [ ] **Minimize state**: Only store what you can't derive or fetch
- [ ] **Use proper state update patterns**: Spread operators for objects/arrays, functional updates for dependent state

## Error Handling & Validation
- [ ] **Use validators on BOTH inputs and outputs**: Validate at boundaries (API calls, function parameters)
- [ ] **Type errors as catch (error: unknown)**: Never assume error shape without checking
- [ ] **Use ConvexError from "convex/values"** for consistent error handling
- [ ] **Validate early**: Check inputs at function entry points
- [ ] **Fail gracefully**: Provide fallbacks and user-friendly error messages
- [ ] **Log errors with context**: Include relevant state/parameters in error logs
- [ ] **Handle edge cases explicitly**: null, undefined, empty arrays, network failures

## Performance Optimization
- [ ] **Avoid .filter on database queries**: Use indexes and query constraints instead
- [ ] **Only .collect small results**: Use pagination for potentially large datasets
- [ ] **Memoize expensive calculations**: Use React.memo, useMemo, useCallback appropriately
- [ ] **Lazy load components and data**: Split code and defer loading until needed
- [ ] **Debounce/throttle event handlers**: Prevent excessive function calls
- [ ] **Batch state updates**: Group related state changes together
- [ ] **Profile before optimizing**: Measure performance bottlenecks first

## Code Organization & Structure
- [ ] **Use helper functions**: Extract logic from components/handlers into pure functions
- [ ] **Consistent file structure**: Group by feature rather than file type
- [ ] **Clear naming conventions**: Use descriptive names (no single letters except loop indices)
- [ ] **Export responsibly**: Only export what's meant to be public API
- [ ] **Barrel exports sparingly**: Avoid circular dependencies
- [ ] **Colocate related code**: Keep tests, styles, types near implementation

## Comments & Documentation
- [ ] **Never delete old comments** unless obviously wrong or obsolete
- [ ] **Include helpful and explanatory comments**: Document WHY, not WHAT
- [ ] **Document all changes and reasoning**: Leave breadcrumbs for future developers
- [ ] **Document edge cases and gotchas**: Highlight non-obvious behavior
- [ ] **Keep comments up to date**: Update when code changes
- [ ] **Use JSDoc for public APIs**: Document parameters, returns, throws

## Security Best Practices
- [ ] **Use access control**: Check ctx.auth.getUserIdentity() for protected operations
- [ ] **Validate all external inputs**: Never trust client data
- [ ] **Sanitize user content**: Prevent XSS, SQL injection

## Tech Stack Specific
- [ ] **Don't use the any type** unless absolutely necessary - leverage TypeScript inference
- [ ] **Await all promises**: Including ctx.scheduler.runAfter, ctx.db.patch

## Database & Data Access (Convex Specific)
- [ ] **Check for redundant indexes**: Remove prefix indexes to reduce overhead
- [ ] **Use `args` and `returns` validators in Convex functions**: For security and type safety on public functions.
- [ ] **Leverage validator type inference**: Avoid redundant TypeScript type annotations for `args` and `returns`.
- [ ] **Use specific validators like `v.union`, `v.literal`, `v.record`, and `v.optional`**: To accurately model your data structures.
- [ ] **Avoid `v.any()`**: Prefer specific validators unless the type is truly dynamic.
- [ ] **Use `Infer<typeof validator>`**: To create TypeScript types from validators for code reuse.
- [ ] **Use specific ID validators**: Always use `v.id("tableName")` for document IDs in function arguments, return types, and schema definitions, not generic types like `v.string()`. This ensures correct type generation and prevents downstream type errors in your application.
- [ ] **Only schedule internal functions**: ctx.runQuery, ctx.runMutation, ctx.scheduler.runAfter
- [ ] **Use runAction only for different runtime**: When you need Node.js features
- [ ] **Avoid sequential ctx.runs in actions**: Combine into single transaction for consistency
- [ ] **Use ctx.run sparingly**: Prefer TypeScript helpers over function calls

## Code Review Checklist
- [ ] **Check for potential race conditions**: Especially in async code
- [ ] **Verify error handling completeness**: All promises caught, all inputs validated
- [ ] **Look for performance issues**: N+1 queries, unnecessary re-renders
- [ ] **Check for security vulnerabilities**: Input validation, auth checks
- [ ] **Look for code smells**: Long functions, deep nesting, duplicate logic