// src/components/entities/AddEntityWizard/steps/PersonInfoStep.tsx

import React from 'react';
import { User, Upload, Mail, Phone, MapPin, Sparkles, Home } from 'lucide-react';
import type { PersonStepProps } from '../AddEntityWizard.types';

export const PersonInfoStep: React.FC<PersonStepProps> = ({ 
  formData, 
  onUpdate,
  personCategory 
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Tell us about them</h2>
        <p className="text-gray-600">Add what you have - you can always update later</p>
      </div>

      <div className="max-w-2xl mx-auto space-y-8">
        {/* Profile and Name Section */}
        <div className="flex items-start gap-8">
          <div className="flex-shrink-0">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                <User size={40} className="text-gray-400" />
              </div>
              <button className="absolute bottom-0 right-0 w-8 h-8 bg-white border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50">
                <Upload size={16} />
              </button>
            </div>
          </div>

          <div className="flex-1 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.person_first_name}
                  onChange={(e) => onUpdate({ person_first_name: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="John"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  type="text"
                  value={formData.person_last_name}
                  onChange={(e) => onUpdate({ person_last_name: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Doe"
                />
              </div>
            </div>

            {(personCategory === 'client_family' || personCategory === 'client') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Birthday</label>
                <input
                  type="date"
                  value={formData.person_birthday}
                  onChange={(e) => onUpdate({ person_birthday: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="border-t pt-6">
          <h3 className="font-medium text-gray-900 mb-4">Contact Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Mail size={16} className="inline mr-1" />
                Email
              </label>
              <input
                type="email"
                value={formData.primary_email}
                onChange={(e) => onUpdate({ primary_email: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Phone size={16} className="inline mr-1" />
                Phone
              </label>
              <input
                type="tel"
                value={formData.primary_phone}
                onChange={(e) => onUpdate({ primary_phone: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="+****************"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <MapPin size={16} className="inline mr-1" />
                Address
              </label>
              <input
                type="text"
                value={formData.primary_address}
                onChange={(e) => onUpdate({ primary_address: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="City, State"
              />
            </div>
          </div>
        </div>

        {/* Quick Notes */}
        <div className="border-t pt-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">Quick Notes</label>
          <textarea
            value={formData.person_notes}
            onChange={(e) => onUpdate({ person_notes: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            rows={3}
            placeholder="Anything important to remember..."
          />
        </div>

        {/* Context-specific tips */}
        {personCategory === 'client' && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <Sparkles size={20} className="text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-blue-900">Pro tip</p>
                <p className="text-sm text-blue-700 mt-1">
                  Next, you&apos;ll be able to add their spouse, employer, and professional team
                </p>
              </div>
            </div>
          </div>
        )}

        {personCategory === 'client_family' && (
          <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
            <div className="flex items-start gap-3">
              <Home size={20} className="text-purple-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-purple-900">Relationship tracking</p>
                <p className="text-sm text-purple-700 mt-1">
                  Make sure to connect them to the primary client in the next step
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};