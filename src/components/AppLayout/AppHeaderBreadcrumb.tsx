"use client";

import { usePathname, useParams } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface BreadcrumbSegment {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

export function AppHeaderBreadcrumb() {
  const pathname = usePathname();
  const params = useParams();

  const formatSegmentLabel = (segment: string): string => {
    // Remove route groups (parentheses)
    const cleanSegment = segment.replace(/^\([^)]*\)$/, "");
    if (!cleanSegment) return "";

    // Handle common patterns
    return cleanSegment
      .split(/[-_]/) // Split on hyphens and underscores
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const isObjectId = (str: string): boolean => {
    return /^[a-f0-9]{24}$/.test(str);
  };

  const isUuid = (str: string): boolean => {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
      str,
    );
  };

  const isDynamicSegment = (segment: string): boolean => {
    return isObjectId(segment) || isUuid(segment) || /^\d+$/.test(segment);
  };

  const generateBreadcrumbs = (): BreadcrumbSegment[] => {
    const segments: BreadcrumbSegment[] = [];

    // Remove leading slash, filter out empty segments and route groups that don't contribute to the path
    const pathSegments = pathname
      .split("/")
      .filter(Boolean)
      .filter((segment) => !/^\([^)]*\)$/.exec(segment)); // Remove route groups like (auth), (app)

    if (
      pathSegments.length > 0 &&
      pathSegments[0] &&
      pathSegments[0].toLowerCase() !== "home"
    ) {
      segments.push({ label: "Home", href: "/home" });
    }

    let currentPath = "";

    for (let i = 0; i < pathSegments.length; i++) {
      const segment = pathSegments[i];
      if (!segment) continue;

      currentPath += `/${segment}`;
      const isLast = i === pathSegments.length - 1;

      // Handle specific known routes
      if (segment === "home") {
        if (isLast) {
          segments.push({ label: "Home", isCurrentPage: true });
        } else {
          segments.push({ label: "Home", href: "/home" });
        }
      } else if (segment === "tasks") {
        if (isLast) {
          segments.push({ label: "Tasks", isCurrentPage: true });
        } else {
          segments.push({ label: "Tasks", href: "/tasks" });
        }
      } else if (segment === "settings") {
        if (isLast) {
          segments.push({ label: "Settings", isCurrentPage: true });
        } else {
          segments.push({ label: "Settings", href: "/settings" });
        }
      } else if (segment === "profile") {
        if (isLast) {
          segments.push({ label: "Profile", isCurrentPage: true });
        } else {
          segments.push({ label: "Profile", href: "/profile" });
        }
      } else if (isDynamicSegment(segment) || params.id) {
        // Handle dynamic segments (IDs, UUIDs, etc.)
        const dynamicId = params.id ? (params.id as string) : segment;
        if (dynamicId) {
          let label = "";

          if (isObjectId(dynamicId)) {
            label = `Task ${dynamicId.slice(0, 8)}...`;
          } else if (isUuid(dynamicId)) {
            label = `Item ${dynamicId.slice(0, 8)}...`;
          } else if (/^\d+$/.test(dynamicId)) {
            label = `#${dynamicId}`;
          } else {
            label = `${dynamicId.slice(0, 10)}${dynamicId.length > 10 ? "..." : ""}`;
          }

          segments.push({
            label,
            isCurrentPage: isLast,
          });
        }
      } else {
        // Generic segment handling with better formatting
        const label = formatSegmentLabel(segment);
        if (label) {
          // Only add if label is not empty
          if (isLast) {
            segments.push({ label, isCurrentPage: true });
          } else {
            segments.push({ label, href: currentPath });
          }
        }
      }
    }

    return segments;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((crumb, index) => (
          <div key={index} className="flex items-center">
            <BreadcrumbItem className={index === 0 ? "hidden md:block" : ""}>
              {crumb.isCurrentPage ? (
                <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink href={crumb.href ?? "#"}>
                  {crumb.label}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < breadcrumbs.length - 1 && (
              <BreadcrumbSeparator
                className={index === 0 ? "hidden md:block" : ""}
              />
            )}
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
