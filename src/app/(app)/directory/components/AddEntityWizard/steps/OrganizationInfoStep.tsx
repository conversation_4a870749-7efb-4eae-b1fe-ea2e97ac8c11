// src/components/entities/AddEntityWizard/steps/OrganizationInfoStep.tsx

import React from 'react';
import { Building2, Upload, Mail, Phone, MapPin, Globe, DollarSign, Users } from 'lucide-react';
import type { OrganizationStepProps } from '../AddEntityWizard.types';

export const OrganizationInfoStep: React.FC<OrganizationStepProps> = ({ 
  formData, 
  onUpdate,
  organizationCategory 
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Organization Details</h2>
        <p className="text-gray-600">Add what you know - you can always update later</p>
      </div>

      <div className="max-w-2xl mx-auto space-y-8">
        {/* Profile and Name Section */}
        <div className="flex items-start gap-8">
          <div className="flex-shrink-0">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center">
                <Building2 size={40} className="text-purple-400" />
              </div>
              <button className="absolute bottom-0 right-0 w-8 h-8 bg-white border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50">
                <Upload size={16} />
              </button>
            </div>
          </div>

          <div className="flex-1 space-y-4">
            <div>
              <label htmlFor="organization-name" className="block text-sm font-medium text-gray-700 mb-1">
                Organization Name <span className="text-red-500">*</span>
              </label>
              <input
                id="organization-name"
                type="text"
                value={formData.organization_name}
                onChange={(e) => onUpdate({ organization_name: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Acme Corporation"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Globe size={16} className="inline mr-1" />
                Industry
              </label>
              <input
                type="text"
                value={formData.organization_industry}
                onChange={(e) => onUpdate({ organization_industry: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                placeholder="Technology, Finance, Healthcare..."
              />
            </div>
          </div>
        </div>

        {/* Organization Details */}
        <div className="border-t pt-6">
          <h3 className="font-medium text-gray-900 mb-4">Additional Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Users size={16} className="inline mr-1" />
                Employee Count
              </label>
              <input
                type="text"
                value={formData.organization_employees_amount}
                onChange={(e) => onUpdate({ organization_employees_amount: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                placeholder="50-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <DollarSign size={16} className="inline mr-1" />
                Annual Revenue
              </label>
              <input
                type="text"
                value={formData.organization_annual_revenue}
                onChange={(e) => onUpdate({ organization_annual_revenue: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                placeholder="$10M - $50M"
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="border-t pt-6">
          <h3 className="font-medium text-gray-900 mb-4">Contact Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Mail size={16} className="inline mr-1" />
                Main Email
              </label>
              <input
                type="email"
                value={formData.primary_email}
                onChange={(e) => onUpdate({ primary_email: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Phone size={16} className="inline mr-1" />
                Main Phone
              </label>
              <input
                type="tel"
                value={formData.primary_phone}
                onChange={(e) => onUpdate({ primary_phone: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                placeholder="+****************"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <MapPin size={16} className="inline mr-1" />
                Headquarters
              </label>
              <input
                type="text"
                value={formData.primary_address}
                onChange={(e) => onUpdate({ primary_address: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
                placeholder="City, State"
              />
            </div>
          </div>
        </div>

        {/* Quick Notes */}
        <div className="border-t pt-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">Quick Notes</label>
          <textarea
            value={formData.organization_notes}
            onChange={(e) => onUpdate({ organization_notes: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
            rows={3}
            placeholder="Key points about this organization..."
          />
        </div>
      </div>
    </div>
  );
};