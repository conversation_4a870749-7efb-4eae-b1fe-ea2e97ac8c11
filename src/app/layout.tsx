import type { Metadata } from "next";
import localFont from "next/font/local";
import "../styles/globals.css";
import "@copilotkit/react-ui/styles.css";
import Providers from "@/components/Providers";
import { Toaster } from "@/components/ui/sonner";
import ErrorBoundary from "@/components/AppScreens/ErrorBoundary";
import { StagewiseToolbar } from "@stagewise/toolbar-next";
import { ReactPlugin } from "@stagewise-plugins/react";
import { CopilotKit } from "@copilotkit/react-core";
import { env } from "@/env";

const sfPro = localFont({
  src: "../../public/sf-pro-text-regular.woff2",
  variable: "--font-sf-pro",
});

export const metadata: Metadata = {
  title: "Caliber",
  description: "Caliber is a platform for managing your business.",
};

const stagewiseConfig = {
  plugins: [ReactPlugin],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${sfPro.variable} antialiased`}>
        <CopilotKit publicApiKey={env.NEXT_PUBLIC_COPILOT_CLOUD_PUBLIC_API_KEY}>
          <ErrorBoundary>
            <Providers>
              <div className="gradient-bg fixed inset-0 -z-10" />
              {children}
              <Toaster />
            </Providers>
          </ErrorBoundary>
        </CopilotKit>
        {process.env.NODE_ENV === "development" && (
          <StagewiseToolbar config={stagewiseConfig} />
        )}
      </body>
    </html>
  );
}
