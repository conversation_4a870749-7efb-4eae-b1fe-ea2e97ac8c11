"use client";

import Board from "./components/Board";
import { api } from "@/../convex/_generated/api";
import { useConvexAuth, useQuery } from "convex/react";
import { LoadingScreen } from "@/components/AppScreens/LoadingScreen";

export default function Home() {
  const { isLoading, isAuthenticated } = useConvexAuth();
  const boardData = useQuery(
    api.tasks.tasksQueries.getBoardData,
    isAuthenticated ? {} : "skip",
  );

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    // This should not happen due to middleware, but as a fallback
    return null;
  }

  return <Board boardData={boardData} />;
}
