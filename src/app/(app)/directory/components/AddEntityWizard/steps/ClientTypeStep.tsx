'use client';

import React from 'react';
import { Check, User, Building } from 'lucide-react';
import type { StepProps } from '../AddEntityWizard.types';

const CLIENT_TYPE_OPTIONS = [
  {
    id: 'individual',
    label: 'Individual Client',
    description: 'A person who has accounts with your firm',
    icon: User,
    color: '#3B82F6',
  },
  {
    id: 'organization',
    label: 'Organizational Client',
    description: 'Trust, foundation, corporation, or other entity',
    icon: Building,
    color: '#8B5CF6',
  },
];

export const ClientTypeStep: React.FC<StepProps> = ({ formData, onUpdate }) => {
  const selectedType = formData.client_type;

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">What type of client is this?</h2>
        <p className="text-gray-600">This helps us collect the right information</p>
      </div>

      <div className="grid grid-cols-1 gap-4 max-w-md mx-auto">
        {CLIENT_TYPE_OPTIONS.map((opt) => (
          <button
            key={opt.id}
            onClick={() => onUpdate({ client_type: opt.id as 'individual' | 'organization' })}
            className={`relative p-4 rounded-xl border-2 transition-all text-left ${
              selectedType === opt.id
                ? ''
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
            style={{
              borderColor: selectedType === opt.id ? opt.color : undefined,
              backgroundColor: selectedType === opt.id ? `${opt.color}1A` : undefined,
            }}
          >
            <div className="flex items-start gap-4">
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                selectedType === opt.id
                  ? ''
                  : 'bg-gray-100 text-gray-600'
              }`}
              style={{
                backgroundColor: selectedType === opt.id ? `${opt.color}33` : undefined,
                color: selectedType === opt.id ? opt.color : undefined,
              }}
              >
                <opt.icon size={24} />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">{opt.label}</h3>
                <p className="text-sm text-gray-600 mt-1">{opt.description}</p>
              </div>
              {selectedType === opt.id && (
                <div className="absolute top-4 right-4">
                  <Check size={20} style={{ color: opt.color }} />
                </div>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};
