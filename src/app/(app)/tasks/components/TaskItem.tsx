"use client";
import { Draggable } from "@hello-pangea/dnd";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { AlertCircle, CheckCircle2, Clock } from "lucide-react";
import type { Task } from "../types";
import { priorityColors } from "../constants";
import { statusConfig } from "../constants";
import { formatStatus } from "@/lib/utils";
import Link from "next/link";

interface TaskItemProps {
  task: Task;
  index: number;
}

const TaskItem = ({ task, index }: TaskItemProps) => {
  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Status icons mapping
  const statusIcons = {
    toDo: <Clock className="mr-1 h-3 w-3" />,
    inProgress: <Clock className="mr-1 h-3 w-3 text-blue-500" />,
    done: <CheckCircle2 className="mr-1 h-3 w-3 text-green-500" />,
    blocked: <AlertCircle className="mr-1 h-3 w-3 text-red-500" />,
  };

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-2 border border-white/20 shadow-sm ${
            snapshot.isDragging ? "shadow-lg ring-2 ring-blue-400/50" : ""
          } bg-white/30 backdrop-blur-sm transition-all hover:bg-white/90`}
          style={{
            ...provided.draggableProps.style,
          }}
        >
          <CardContent className="p-3 pb-2">
            <div className="mb-2 flex items-start justify-between">
              <Link className="hover:underline" href={`/tasks/${task.id}`}>
                <h4 className="line-clamp-2 text-sm font-medium text-slate-800">
                  {task.title}
                </h4>
              </Link>
              <Badge
                className={`${priorityColors[task.priority]} ml-2 px-2 py-0.5 text-xs`}
              >
                {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
              </Badge>
            </div>

            {task.description && (
              <p className="mb-2 line-clamp-2 text-xs text-slate-600">
                {task.description}
              </p>
            )}

            <Badge
              variant="outline"
              className={`flex w-fit items-center text-xs ${statusConfig[task.status]?.color || ""}`}
            >
              {statusIcons[task.status]}
              {formatStatus(task.status)}
            </Badge>
          </CardContent>

          <CardFooter className="flex items-center justify-between border-t border-slate-200/50 p-2">
            <div className="flex items-center">
              <Avatar className="mr-1 h-5 w-5">
                <AvatarImage
                  src={task.assignee.avatar || "/placeholder.svg"}
                  alt={task.assignee.name}
                />
                <AvatarFallback>
                  {getInitials(task.assignee.name)}
                </AvatarFallback>
              </Avatar>
              <span className="truncate text-xs text-slate-600">
                {task.assignee.name}
              </span>
            </div>
          </CardFooter>
        </Card>
      )}
    </Draggable>
  );
};

export default TaskItem;
