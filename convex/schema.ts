// schema.ts

import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { statusesConvexType, priorityConvexType } from "./types";

export default defineSchema({
  // User profiles table
  userProfiles: defineTable({
    clerkUserId: v.string(), // Clerk user identifier
    firstName: v.string(), // User's first name
    lastName: v.string(), // User's last name
    email: v.string(), // User's email address
    imageUrl: v.optional(v.string()), // User's image URL
  })
    .index("by_email", ["email"])
    .index("by_clerkUserId", ["clerkUserId"]),

  tenantMemberships: defineTable({
  clerkTenantId:    v.string(),            // Canonical tenant key from Clerk
  clerkUserId: v.string(),   //canonical user key from clerk
})
  // Primary look-ups
.index("by_org_user",  ["clerkTenantId", "clerkUserId"])
.index("by_user_org", ["clerkUserId", "clerkTenantId"]),



/*──────────  Universal identity card  ─────────*/
entities: defineTable({
  clerkTenantId: v.string(),
  entity_type: v.union(
    v.literal("person"),
    v.literal("organization")
  ),
  entity_display_name: v.string(),
  primary_email: v.optional(v.string()),
  primary_phone: v.optional(v.string()),
  primary_address: v.optional(v.string()),
  entity_image_url: v.optional(v.string()),
  /* Shared optional fields */
  entity_links: v.optional(
    v.array(
      v.object({
        type: v.union(
          v.literal("linkedin"),        // LinkedIn profile or page
          v.literal("domain_name"),     // Main corporate domain or personal site
          v.literal("socialmedia")      // X, Instagram, TikTok, etc.
        ),
        url:  v.string(),
      }),
    ),
  ),

  entity_ai_short_description: v.optional(v.string()),
  entity_ai_summary:        v.optional(v.string()),
  entity_last_activity: v.optional(v.number()), // Unix timestamp in milliseconds
})
  .index("by_clerkTenantId", ["clerkTenantId"])
  .index("by_clerkTenantId_entity_type", ["clerkTenantId", "entity_type"])
  .index("by_clerkTenantId_display_name", ["clerkTenantId", "entity_display_name"])
  .index("by_clerkTenantId_primary_email", ["clerkTenantId", "primary_email"]),

/*──────────  Person-only attributes  ─────────*/
person_details: defineTable({
  clerkTenantId: v.string(),
  entity_id:   v.id("entities"),
  person_notes: v.optional(v.string()),
  person_first_name:  v.string(),
  person_last_name:   v.optional(v.string()),
  person_birthday:    v.optional(v.number()),      // Unix timestamp in milliseconds
  person_emails: v.optional(
    v.array(
      v.object({
        email: v.string(),
        label: v.union(
          v.literal("primary"),
          v.literal("other")
        ),
      })
    )
  ),
  person_ai_deep_research: v.optional(v.string()),
  person_do_not_contact: v.optional(v.boolean()),
  person_phones: v.optional(v.array(v.object({ number: v.string(), label: v.union(v.literal('primary'), v.literal('work'), v.literal('other')) }))),
  person_addresses: v.optional(v.array(v.object({ line: v.string(), label: v.union(v.literal('primary'), v.literal('secondary'), v.literal('other')) }))),
})
  .index("by_clerkTenantId_entity_id",   ["clerkTenantId", "entity_id"])
  .index("by_clerkTenantId_person_last_name", ["clerkTenantId", "person_last_name"]),

/*──────────  Organization-only attributes  ───*/
organization_details: defineTable({
  clerkTenantId: v.string(), //this is only used for data isolation and scoping
  entity_id:       v.id("entities"),
  organization_notes: v.optional(v.string()),
  organization_employees_amount:v.optional(v.number()),
  organization_annual_revenue:  v.optional(v.number()),
  organization_industry:        v.optional(v.string()),
  organization_ai_deep_research: v.optional(v.string()),
  organization_phones: v.optional(v.array(v.object({ number: v.string(), label: v.union(v.literal('primary'), v.literal('work'), v.literal('other')) }))),
  organization_addresses: v.optional(v.array(v.object({ line: v.string(), label: v.union(v.literal('primary'), v.literal('secondary'), v.literal('other')) }))),
 })
  .index("by_clerkTenantId_entity_id", ["clerkTenantId", "entity_id"]),

/*──────────  Wealth-management client flag  ──*/
clients: defineTable({
  clerkTenantId: v.string(),
  entity_id: v.id("entities"),

  client_status: v.union(
    v.literal("active"),
    v.literal("on_hold"),
    v.literal("prospect"),
    v.literal("former")
  ),
  client_tier:      v.optional(v.string()),
  client_since: v.optional(v.number()),
  client_last_contact: v.optional(v.number()),
  client_net_worth: v.optional(v.number()),
  client_communication_frequency: v.union(
    v.literal('weekly'),
    v.literal('monthly'),
    v.literal('quarterly'),
    v.literal('twice_yearly'),
    v.literal('annually'),
    v.literal('as_needed')
  ),})
  .index("by_clerkTenantId", ["clerkTenantId", "entity_id"])
  .index("by_clerkTenantId_status",    ["clerkTenantId", "client_status"]),

/*──────────  Graph edges (family, employment…) ─*/
entity_relationships: defineTable({
  clerkTenantId:     v.string(),
  entity_1_id:   v.id("entities"),
  entity_2_id:   v.id("entities"),

  relationship_type: v.string(),          // "spouse", "parent", "employment", …
  relationship_active:         v.boolean(),
  relationship_start:    v.optional(v.number()),
  relationship_end:      v.optional(v.number()),
  metadata:          v.optional(v.any()),
})
  .index("by_clerkTenantId_entity1", ["clerkTenantId", "entity_1_id"])
  .index("by_clerkTenantId_entity2", ["clerkTenantId", "entity_2_id"])
  .index("by_clerkTenantId_relationship_type", ["clerkTenantId", "relationship_type"]),
  
  // Tasks table
  tasks: defineTable({
    clerkTenantId: v.string(), // Tenant ID for strict isolation
    creatorId: v.id("userProfiles"), // User profile id of the creator
    title: v.string(), // Brief title of the task
    task_is_private: v.boolean(),
    description: v.string(), // Detailed description
    status: statusesConvexType, // Task workflow state
    assigneeId: v.string(), // User profile id of the assignee
    priority: priorityConvexType, // Priority level (e.g. 1-5)
    dueDate: v.optional(v.number()), // Optional due date
  })
    .index("by_clerkTenantId", ["clerkTenantId"])
    .index("by_clerkTenantId_assigneeId_dueDate", [
      "clerkTenantId",
      "assigneeId",
      "dueDate",
    ])
    .index("by_clerkTenantId_status", ["clerkTenantId", "status"])
    .index("by_clerkTenantId_priority", ["clerkTenantId", "priority"]),

  // Comments table
  comments: defineTable({
    clerkTenantId: v.string(), // Organization scoping
    parentId: v.string(), // Task._id
    authorId: v.id("userProfiles"), // User profile id of the author
    authorName: v.string(), // Name of the author
    authorImage: v.string(), // Image of the author
    content: v.string(), // Comment content
  })
    .index("by_clerkTenantId_parentId", ["clerkTenantId", "parentId"])
    .index("by_clerkTenantId_authorId", ["clerkTenantId", "authorId"]),
});
