'use client';

// src/app/(app)/directory/components/AddEntityWizard/steps/PersonCategoryStep.tsx

import React from 'react';
import { Check } from 'lucide-react';
import { PERSON_CATEGORIES } from '../AddEntityWizard.constants';
import type { PersonStepProps, PersonCategory } from '../AddEntityWizard.types';

export const PersonCategoryStep: React.FC<PersonStepProps> = ({
  onUpdate,
  personCategory
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Who are you adding?</h2>
        <p className="text-gray-600">Select the category that best describes this contact</p>
      </div>
      
      <div className="space-y-3 max-w-2xl mx-auto">
        {PERSON_CATEGORIES.map((cat) => (
          <button
            key={cat.id}
            onClick={() => onUpdate({ person_category: cat.id as PersonCategory })}
            className={`relative w-full p-4 rounded-xl border-2 transition-all text-left ${
              personCategory === cat.id
                ? ''
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
            style={{
              borderColor: personCategory === cat.id ? cat.color : undefined,
              backgroundColor: personCategory === cat.id ? `${cat.color}1A` : undefined,
            }}
          >
            <div className="flex items-start gap-4">
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0 ${
                personCategory === cat.id
                  ? ''
                  : 'bg-gray-100 text-gray-600'
              }`}
              style={{
                backgroundColor: personCategory === cat.id ? `${cat.color}33` : undefined,
                color: personCategory === cat.id ? cat.color : undefined,
              }}
              >
                <cat.icon size={24} />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-lg">{cat.label}</h3>
                <p className="text-sm text-gray-600 mt-1 leading-relaxed">{cat.description}</p>
              </div>
              {personCategory === cat.id && (
                <div className="absolute top-4 right-4">
                  <Check size={20} style={{ color: cat.color }} />
                </div>
              )}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};
