import { v, ConvexError } from "convex/values";
import { mutation } from "../_generated/server";
import { statusesConvexType, priorityConvexType } from "../types";
import type { Id } from "convex/_generated/dataModel";
import type { Priority, Status } from "@/app/(app)/tasks/types";
import { api } from "../_generated/api";
import { getAuthenticatedOrgContext } from "../auth";

export const createTask = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    status: statusesConvexType,
    priority: priorityConvexType,
    assigneeId: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }
    // Get user profile for creatorId
    const user = await ctx.runQuery(
      api.userProfiles.userProfilesQueries.getUserByTokenIdentifier,
      {
        tokenIdentifier: identity.tokenIdentifier,
      },
    );
    if (!user) {
      throw new ConvexError("User not found.");
    }
    await ctx.db.insert("tasks", {
      title: args.title,
      description: args.description,
      status: args.status,
      priority: args.priority,
      assigneeId: args.assigneeId,
      clerkTenantId: clerkTenantId,
      creatorId: user._id,
      task_is_private: false,
    });
    return null;
  },
});

export const deleteTask = mutation({
  args: {
    taskId: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);
    const task = await ctx.db.get(args.taskId as Id<"tasks">);
    if (!task) {
      throw new ConvexError("Task not found");
    }
    if (task.clerkTenantId !== clerkTenantId) {
      throw new ConvexError("You are not authorized to delete this task");
    }
    await ctx.db.delete(args.taskId as Id<"tasks">);
    return null;
  },
});

export const updateTask = mutation({
  args: {
    taskId: v.string(),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(statusesConvexType),
    priority: v.optional(priorityConvexType),
    assigneeId: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);

    const taskToUpdate = await ctx.db.get(args.taskId as Id<"tasks">);
    if (!taskToUpdate) {
      throw new ConvexError("Task not found");
    }

    if (taskToUpdate.clerkTenantId !== clerkTenantId) {
      throw new ConvexError("You are not authorized to update this task");
    }

    const task: {
      title?: string;
      description?: string;
      status?: Status;
      priority?: Priority;
      assigneeId?: string;
    } = {};
    if (args.title) task.title = args.title;
    if (args.description) task.description = args.description;
    if (args.status) task.status = args.status;
    if (args.priority) task.priority = args.priority;
    if (args.assigneeId) task.assigneeId = args.assigneeId;
    await ctx.db.patch(args.taskId as Id<"tasks">, task);
    return null;
  },
});
