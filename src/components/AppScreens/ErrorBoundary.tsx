"use client";

import { useEffect } from "react";
import { Button } from "../ui/button";

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

export default function ErrorBoundary({ children }: ErrorBoundaryProps) {
  useEffect(() => {
    const handleError = (error: unknown) => {
      // Ensure we always log an Error object
      if (error instanceof Error) {
        console.error("Error caught by boundary:", error);
      } else {
        console.error("Error caught by boundary:", new Error(String(error)));
      }
    };

    const errorListener = (event: ErrorEvent) => handleError(event.error);
    const rejectionListener = (event: PromiseRejectionEvent) =>
      handleError(event.reason);

    window.addEventListener("error", errorListener);
    window.addEventListener("unhandledrejection", rejectionListener);

    return () => {
      window.removeEventListener("error", errorListener);
      window.removeEventListener("unhandledrejection", rejectionListener);
    };
  }, []);

  return <>{children}</>;
}

export function ErrorBoundaryFallback() {
  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gray-50">
      <div className="text-center">
        <h2 className="mb-2 text-2xl font-semibold text-gray-900">
          Something went wrong
        </h2>
        <p className="mb-4 text-gray-600">
          We apologize for the inconvenience. Please try refreshing the page.
        </p>
        <Button
          onClick={() => window.location.reload()}
          className="rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
        >
          Refresh Page
        </Button>
      </div>
    </div>
  );
}
