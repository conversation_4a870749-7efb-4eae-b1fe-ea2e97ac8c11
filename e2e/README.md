# E2E Testing with Play<PERSON>

This directory contains End-to-End (E2E) tests for the Caliber application using modern Playwright testing practices with Clerk authentication integration.

## Quick Start

1. **Install dependencies** (already done):
   ```bash
   pnpm install
   ```

2. **Configure environment variables**:
   - Copy `e2e/.env.example` to `e2e/.env.test` and update with your Clerk test keys
   - Set `CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY`

3. **Run tests**:
   ```bash
   # Start the dev server in one terminal
   pnpm dev
   
   # Run tests in another terminal
   pnpm test:e2e
   ```

## Available Scripts

- `pnpm test:e2e` - Run all E2E tests
- `pnpm test:e2e:ui` - Run tests with Playwright UI mode
- `pnpm test:e2e:debug` - Run tests in debug mode
- `pnpm test:e2e:headed` - Run tests in headed mode (visible browser)
- `pnpm test:e2e:report` - Show test report
- `pnpm test:e2e:codegen` - Generate test code using Playwright

## Project Structure

```
e2e/
├── README.md                 # This file
├── global-setup.ts           # Clerk authentication setup
├── mcp-config.json          # MCP server configuration
├── .env.test                # Test environment variables
├── tests/
│   └── app-header.spec.ts   # AppHeader component tests
└── utils/
    └── auth-helpers.ts      # Authentication utilities
```

## Test Features

### Modern Authentication with Clerk
- Uses Clerk's official testing helpers
- Global setup for efficient authentication
- **Dynamic storage state generation** - auth files created at runtime, not committed to git
- Session caching to avoid repeated logins  
- Browser context isolation for test independence

### AI-Enhanced Testing (MCP Server)
- Playwright MCP server integration available
- Autonomous test discovery capabilities
- Natural language test creation through Claude
- Self-healing tests that adapt to UI changes

### Cross-Browser Testing
- Tests run on Chromium, Firefox, and WebKit
- Responsive design testing across viewports
- Consistent behavior validation

## Environment Configuration

### Required Environment Variables

Create `e2e/.env.test` with:

```bash
# Clerk Authentication
CLERK_PUBLISHABLE_KEY=pk_test_your_key_here
CLERK_SECRET_KEY=sk_test_your_key_here

# Application URLs
PLAYWRIGHT_BASE_URL=http://localhost:3000
PLAYWRIGHT_HEADED=false

# MCP Server Configuration
PLAYWRIGHT_MCP_ENABLED=true
```

### Clerk Setup

1. Create a test environment in your Clerk dashboard
2. Generate test API keys for your test environment
3. Create test users and organizations
4. Update the environment variables

### Security Note

The authentication state files (`playwright/.clerk/*.json`) are **automatically generated at runtime** and excluded from git tracking. These files contain sensitive JWTs and cookies that should never be committed to version control.

## Test Implementation

### AppHeader Tests

The `app-header.spec.ts` file contains comprehensive tests for:

- User avatar and profile dropdown functionality
- Organization switcher visibility and interaction
- Profile modal opening
- Breadcrumb navigation
- Responsive design across devices
- Proper styling and layout

### Best Practices

- **Accessibility-First Selectors**: Uses semantic selectors like `getByRole()`, `getByLabel()`
- **Proper Waiting**: Implements robust waiting strategies for dynamic content
- **Test Isolation**: Each test is independent with proper setup/teardown
- **Cross-Browser**: Tests run consistently across different browsers

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Verify Clerk keys are correct
   - Check test user has proper organization access
   - Ensure global setup is configured correctly

2. **Timeout Errors**:
   - Increase timeout values in test files
   - Check if dev server is running
   - Verify network connectivity

3. **Element Not Found**:
   - Use Playwright's debug mode: `pnpm test:e2e:debug`
   - Check if selectors match current UI
   - Verify authentication state

### Debug Commands

```bash
# Run in debug mode
pnpm test:e2e:debug

# Run with visible browser
pnpm test:e2e:headed

# Generate new selectors
pnpm test:e2e:codegen

# View test report
pnpm test:e2e:report
```

## MCP Server Integration

The Playwright MCP server enables AI-powered testing features:

### Starting MCP Server

```bash
npx @playwright/mcp@latest --headless --port 3001
```

### Features Available

- Autonomous app exploration
- AI-powered test generation
- Natural language test creation
- Self-healing test maintenance

### Configuration

MCP server configuration is available in `mcp-config.json`. Customize based on your needs.

## CI/CD Integration

### GitHub Actions

Add to your workflow:

```yaml
- name: Install Playwright
  run: pnpm test:e2e:install

- name: Run E2E Tests
  run: pnpm test:e2e
  env:
    CLERK_PUBLISHABLE_KEY: ${{ secrets.CLERK_PUBLISHABLE_KEY }}
    CLERK_SECRET_KEY: ${{ secrets.CLERK_SECRET_KEY }}
```

### Docker Support

Tests can run in containerized environments. Playwright handles browser installation automatically.

## Next Steps

1. **Expand Test Coverage**: Add tests for other components and user flows
2. **Add Visual Testing**: Implement screenshot comparison tests
3. **Performance Testing**: Add performance assertions
4. **API Testing**: Include API endpoint testing alongside E2E tests
5. **Mobile Testing**: Expand mobile viewport testing

## Resources

- [Playwright Documentation](https://playwright.dev/)
- [Playwright Testing](https://playwright.dev/docs/writing-tests)
- [Clerk Testing Documentation](https://clerk.com/docs/testing/playwright/overview)
- [Playwright MCP Server](https://github.com/microsoft/playwright-mcp)