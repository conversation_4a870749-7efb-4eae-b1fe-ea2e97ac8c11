---
description: 
globs: 
alwaysApply: false
---
Frontend Actions
Learn how to enable your Copilot to take actions in the frontend.

Let the Copilot Take Action
useCopilotAction
In addition to understanding state, you can empower the copilot to take actions. Use the useCopilotAction hook to define specific tasks that the copilot can perform based on user input.

YourComponent.tsx

"use client" // only necessary if you are using Next.js with the App Router. 
import { useCopilotAction } from "@copilotkit/react-core"; 
 
export function MyComponent() {
  const [todos, setTodos] = useState<string[]>([]);
 
  // Define Copilot action
  useCopilotAction({
    name: "addTodoItem",
    description: "Add a new todo item to the list",
    parameters: [
      {
        name: "todoText",
        type: "string",
        description: "The text of the todo item to add",
        required: true,
      },
    ],
    handler: async ({ todoText }) => {
      setTodos([...todos, todoText]);
    },
  });
 
  return (
    <ul>
      {todos.map((todo, index) => (
        <li key={index}>{todo}</li>
      ))}
    </ul>
  );
}
Changing where/when the action is executed
Specify "use client" (Next.js App Router)
This is only necessary if you are using Next.js with the App Router.

YourComponent.tsx

"use client"
Like other React hooks such as useState and useEffect, this is a client-side hook. If you're using Next.js with the App Router, you'll need to add the "use client" directive at the top of any file using this hook.

Test it out!
After defining the action, ask the copilot to perform the task. For example, you can now ask the copilot to "select an employee" by specifying the employeeId.