import { test, expect } from '@playwright/test';

test.describe('AddEntityWizard E2E Tests', () => {
  test('should create a new person entity and navigate to their page', async ({ page }) => {
    // Generate unique name with timestamp
    const now = new Date();
    const firstName = `First${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
    const lastName = `Last${String(now.getHours()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;
    const fullName = `${firstName} ${lastName}`;

    await page.goto('http://localhost:3000/directory');
    
    // Wait for page to be fully loaded and handle any overlays
    await page.waitForLoadState('networkidle');
    
    // Scroll to top to ensure button is not obscured by header
    await page.evaluate(() => window.scrollTo(0, 0));
    
    // Scroll the Add Contact button into view if needed
    await page.getByRole('button', { name: 'Add Contact' }).scrollIntoViewIfNeeded();
    
    await page.getByRole('button', { name: 'Add Contact' }).click();
    await page.getByRole('button', { name: 'Client An individual or' }).click();
    await page.getByRole('button', { name: 'Continue' }).click();
    await page.getByRole('button', { name: 'Individual Client A person' }).click();
    await page.getByRole('button', { name: 'Continue' }).click();
    await page.getByRole('textbox', { name: 'John', exact: true }).fill(firstName);
    await page.getByRole('textbox', { name: 'Doe' }).fill(lastName);
    await page.getByRole('button', { name: 'Continue' }).click();
    await page.getByRole('button', { name: 'Create Contact' }).click();
    
    // Wait for wizard to close and entity to appear in directory
    await expect(page.getByRole('heading', { name: fullName })).toBeVisible();
  });

  test('should create a new organization entity and navigate to its page', async ({ page }) => {
    // Generate unique name with DDHHMMSS timestamp
    const now = new Date();
    const timestamp = String(now.getDate()).padStart(2, '0') + 
                     String(now.getHours()).padStart(2, '0') + 
                     String(now.getMinutes()).padStart(2, '0') + 
                     String(now.getSeconds()).padStart(2, '0');
    const orgName = `TestCorp-${timestamp}`;

    await page.goto('http://localhost:3000/directory');
    
    // Wait for page to be fully loaded and handle any overlays
    await page.waitForLoadState('networkidle');
    
    // Scroll to top to ensure button is not obscured by header
    await page.evaluate(() => window.scrollTo(0, 0));
    
    // Scroll the Add Contact button into view if needed
    await page.getByRole('button', { name: 'Add Contact' }).scrollIntoViewIfNeeded();
    
    await page.getByRole('button', { name: 'Add Contact' }).click();
    
    // Step 1: Select Organization Category
    await page.getByText('Organization').filter({ hasText: 'Companies' }).click();
    await page.getByRole('button', { name: 'Continue' }).click();

    // Step 2: Fill in Basic Information (no organization type step for business contacts)
    await page.getByLabel('Organization Name').fill(orgName);
    await page.getByRole('button', { name: 'Continue' }).click();

    // Step 3: Skip Connections and Finish
    await page.getByRole('button', { name: 'Create Contact' }).click();
    
    // Assertion: Wait for wizard to close and entity to appear on its page
    await expect(page.getByRole('heading', { name: orgName })).toBeVisible();
  });
});
