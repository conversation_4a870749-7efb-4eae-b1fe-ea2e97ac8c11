# System Patterns

## System Architecture
Caliber follows a client-server architecture. The frontend is a Next.js application, and the backend leverages Convex for real-time database and serverless functions. Authentication is handled by <PERSON>.

## Key Technical Decisions
- **Next.js:** <PERSON>sen for its React framework capabilities, server-side rendering (SSR), and API routes, providing a robust foundation for the frontend.
- **Convex:** Selected for its real-time capabilities, integrated database, and serverless functions, simplifying backend development and enabling collaborative features.
- **Clerk:** Utilized for authentication due to its comprehensive features, ease of integration, and focus on developer experience.
- **Shadcn/ui:** Used for UI components to ensure a consistent and modern design, and accelerate frontend development.

## Design Patterns in Use
- **Component-Based Architecture:** Frontend is built with reusable React components.
- **Serverless Functions:** Backend logic is implemented as serverless functions (Convex mutations and queries).
- **Real-time Data Synchronization:** Convex's real-time capabilities are leveraged for immediate UI updates across clients.

## Component Relationships
- **Frontend (Next.js):**
    - `src/app/`: Contains page-level components and routing.
    - `src/components/`: Reusable UI components.
    - `src/app/tasks/`: Task-specific components and pages.
    - `src/components/entities/AddEntityWizard/`: Multi-step form for creating people and organizations.
- **Backend (Convex):**
    - `convex/tasks/`: Defines mutations and queries for task management.
    - `convex/users/`: Defines mutations and queries for user management.
    - `convex/entities/`: Defines mutations and queries for entity management.
    - `convex/lib/`: Utility functions and authentication helpers.

## Critical Implementation Paths
- **Authentication & Routing Flow:**
  1. **Initial Visit:** Unauthenticated users are directed to the marketing page (`/`) which contains a `SignInButton`.
  2. **Authentication:** Users sign in or sign up via Clerk's hosted pages. Upon successful authentication, Clerk redirects them to `/home`.
  3. **Middleware Validation:** The Next.js middleware intercepts the request to `/home` and validates the user's JWT provided by Clerk.
     - If `userId` is missing, the user is redirected to `/login`.
     - If `orgId` is missing, the user is redirected to `/org-selection`.
     - If both are present, access is granted to the protected application routes.
- **Organization Management:**
  - The `/org-selection` page uses Clerk's `<OrganizationList>` component to handle all organization-related actions: creating a new organization, selecting an existing one, or accepting invitations.
  - After an organization is created or selected, the user is redirected back to `/home`, and the middleware grants access.
- **Authorization & Data Scoping:**
  - All Convex functions (queries, mutations, actions) verify the user's identity and organization context using a helper like `getAuthenticatedOrgContext(ctx)`.
  - Data is automatically and securely scoped to the user's active organization by using the `clerkTenantId` from the JWT in all database queries (e.g., `.withIndex("by_clerkTenantId", q => q.eq("clerkTenantId", clerkTenantId))`). This ensures strict tenant isolation.
- **Task CRUD Operations:** Frontend interactions trigger Convex mutations and queries for creating, reading, updating, and deleting tasks, all scoped to the active organization.
- **Real-time Task Updates:** Changes to tasks in Convex automatically propagate to all connected clients within the same organization.
- **Comment System:** Adding and displaying comments on tasks, leveraging Convex for storage and real-time updates, with all data scoped by organization.
- **Entity Creation Flow:**
  - The `AddEntityWizard` component guides users through a multi-step process to create new entities (people or organizations).
  - The wizard uses custom hooks (`useEntityForm`, `useRelationships`, `useEntitySearch`) to manage state and logic.
  - Upon submission, a Convex mutation (`api.entities.createEntity`) is called to create the new entity in the database.
  - The data is scoped to the active organization using the `clerkTenantId`.
