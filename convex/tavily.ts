"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { tavily, type TavilyClient } from "@tavily/core";

// Types based on Tavily API documentation
interface ImageResult {
  url: string;
  description?: string;
}

interface Result {
  title: string;
  url: string;
  content: string;
  score: number;
  rawContent?: string;
  publishedDate?: string;
}

interface TavilySearchResponse {
  results: Result[];
  query: string;
  responseTime: number;
  answer?: string;
  images?: string[] | ImageResult[];
}

const tavilyClient: TavilyClient = tavily({
  apiKey: process.env.TAVILY_API_KEY!,
});

export const tavilySearch = action({
  args: {
    query: v.string(),
    searchDepth: v.optional(v.union(v.literal("basic"), v.literal("advanced"))),
    topic: v.optional(v.union(v.literal("general"), v.literal("news"))),
    days: v.optional(v.number()),
    timeRange: v.optional(
      v.union(
        v.literal("year"),
        v.literal("month"),
        v.literal("week"),
        v.literal("day"),
        v.literal("y"),
        v.literal("m"),
        v.literal("w"),
        v.literal("d")
      )
    ),
    maxResults: v.optional(v.number()),
    chunksPerSource: v.optional(v.number()),
    includeImages: v.optional(v.boolean()),
    includeImageDescriptions: v.optional(v.boolean()),
    includeAnswer: v.optional(v.boolean()),
    includeRawContent: v.optional(
      v.union(v.literal(false), v.literal("markdown"), v.literal("text"))
    ),
    includeDomains: v.optional(v.array(v.string())),
    excludeDomains: v.optional(v.array(v.string())),
    country: v.optional(v.string()),
    timeout: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<TavilySearchResponse> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Unauthenticated call to tavilySearch action");
    }

    if (!process.env.TAVILY_API_KEY) {
      throw new Error(
        "TAVILY_API_KEY environment variable is not set in the Convex dashboard."
      );
    }

    const { query, ...options } = args;
    const response: TavilySearchResponse = await tavilyClient.search(query, options);

    return response;
  },
});
