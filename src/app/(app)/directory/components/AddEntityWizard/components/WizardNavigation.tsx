// src/components/entities/AddEntityWizard/components/WizardNavigation.tsx

import React from 'react';
import { ArrowLeft, ArrowRight, Check } from 'lucide-react';

interface WizardNavigationProps {
  currentStep: number;
  totalSteps: number;
  canProceed: boolean;
  onBack: () => void;
  onNext: () => void;
  onSubmit: () => void;
  isSubmitting?: boolean;
}

export const WizardNavigation: React.FC<WizardNavigationProps> = ({
  currentStep,
  totalSteps,
  canProceed,
  onBack,
  onNext,
  onSubmit,
  isSubmitting = false
}) => {
  return (
    <div className="flex justify-between">
      <button
        onClick={onBack}
        disabled={currentStep === 1}
        className={`
          flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors
          ${currentStep === 1
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-700 hover:bg-gray-100'
          }
        `}
      >
        <ArrowLeft size={20} />
        Back
      </button>

      {currentStep < totalSteps ? (
        <button
          onClick={onNext}
          disabled={!canProceed}
          className={`
            flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors
            ${!canProceed
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
            }
          `}
        >
          Continue
          <ArrowRight size={20} />
        </button>
      ) : (
        <button 
          onClick={onSubmit}
          disabled={isSubmitting || !canProceed}
          className={`
            flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors
            ${isSubmitting || !canProceed
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
            }
          `}
        >
          <Check size={20} />
          {isSubmitting ? 'Creating...' : 'Create Contact'}
        </button>
      )}
    </div>
  );
};