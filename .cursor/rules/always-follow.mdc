---
description: 
globs: 
alwaysApply: true
---
- <PERSON>
- **Framework**: Next.js (React)
- **Styling**: Tailwind CSS, ShadCN UI components
- **AI Integration**: Use Vercel AI SDK and it's ai handler. 
- **Data Storage**: Convex.dev DB. This uses real-time updates, so no need to force refresh.

## Comments
- Never delete old comments unless they're obviously wrong or obsolete. 
- Include lots of helpful and explanatory comments in your code. Always write well-documented code. 
- Document all changes and their reasoning in the comments that you write. 
- When writing comments, use clear and easy-to-understand language. Write in short sentences.

# Guidelines
- Don't use 'any' type unless absolutely necessary
- use pnpm as the package manager. 
- convex automatically provdes the _id and _creationTime fields for all tables. 
- Await all promises. If you don’t await all promises (e.g. ctx.scheduler.runAfter, ctx.db.patch), you risk missing errors or failing to run the intended operations.
- Avoid .filter on database queries
- use ConvexError from "convex/values" for error handling. Type errors as catch (error: unknown)
- only .collect small results: .collect returns all matching documents, which can be costly in bandwidth and cause re-runs if any document changes. For potentially large or unbounded results, use an index to narrow results or use .paginate instead of .collect.