import React, { useState } from "react";
import Image from "next/image";
import { Mail, Phone, MapPin, Briefcase, Users, Crown, Heart, Baby, Cake, Building, Camera, Loader2, Globe } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useAction, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import type { Doc, Id } from "@/../convex/_generated/dataModel";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";

type DirectoryItem = Doc<"entities"> & {
  details?: Partial<Doc<"person_details">> & Partial<Doc<"organization_details">>;
  title?: string;
  location?: string;
  company?: string;
  familyMemberCount?: number;
  relationshipType?: "spouse" | "child" | "parent" | "sibling" | "other";
  isPrincipal?: boolean;
  birthday?: string;
  age?: number;
  employeeCount?: number;
  website?: string;
};

interface DirectoryCardLargeProps {
  variant?: "principal" | "family" | "professional" | "organization";
  item?: DirectoryItem;
}

interface SearchResult {
  url: string;
}

function DirectoryCardLarge({ variant = "professional", item: person }: DirectoryCardLargeProps) {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [imageResults, setImageResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const searchImages = useAction(api.serp.serpImagesLight);
  const updateEntityImage = useMutation(api.entities.entitiesMutations.updateEntityImage);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const handleSearchClick = async () => {
    if (!person) return;
    setIsLoading(true);
    setImageResults([]);

    let query = "find large images suitable for a profile image.";
    query += `\nName: ${person.entity_display_name}`;
    if (person.company) {
      query += `\nOrganization Name: ${person.company}`;
    }
    const domainLink = person.entity_links?.find(link => link.type === 'domain_name');
    if (domainLink) {
      query += `\nURL: ${domainLink.url}`;
    }

    try {
      const result = await searchImages({ q: query });
      
      if (result.images_results && Array.isArray(result.images_results)) {
          const formattedImages = result.images_results
            .map(img => ({ url: img.original ?? img.thumbnail }))
            .filter(img => img.url);
          setImageResults(formattedImages.slice(0, 3));
      } else {
          toast.info("No images found.");
      }

    } catch (error) {
      console.error("Image search failed:", error);
      toast.error("Failed to search for images.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageSelect = async (imageUrl: string) => {
    if (!person?._id) {
      return;
    }

    try {
      await updateEntityImage({
        id: person._id as Id<"entities">,
        imageUrl: imageUrl,
      });
      toast.success("Image updated successfully!");
      setIsPopoverOpen(false);
    } catch (error) {
      console.error("Failed to update image:", error);
      toast.error("Failed to update image.");
    }
  };

  // Centralized color definition - one color per variant
  const getAccentColor = () => {
    switch (variant) {
      case "principal":
        return "text-amber-200";
      case "family":
        return "text-rose-100";
      case "organization":
        return "text-emerald-100";
      default:
        return "text-blue-100";
    }
  };

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case "spouse":
        return <Heart className="h-4 w-4 text-white" />;
      case "child":
        return <Baby className="h-4 w-4 text-white" />;
      default:
        return <Users className="h-4 w-4 text-white" />;
    }
  };

  const getRelationshipColor = (type: string) => {
    switch (type) {
      case "spouse":
        return "from-rose-500/80 to-pink-500/80";
      case "child":
        return "from-blue-500/80 to-cyan-500/80";
      case "parent":
        return "from-emerald-500/80 to-green-500/80";
      default:
        return "from-gray-500/80 to-slate-500/80";
    }
  };


  // Get background color based on variant
  const getBackgroundGradient = () => {
    switch (variant) {
      case "principal":
        return "from-amber-900/70 via-amber-900/10 via-50% to-transparent";
      case "family":
        return "from-rose-900/70 via-rose-900/10 via-50% to-transparent";
      case "organization":
        return "from-emerald-900/70 via-emerald-900/10 via-50% to-transparent";
      default:
        return "from-blue-900/70 via-blue-900/10 via-50% to-transparent";
    }
  };

  // Get fallback background based on variant
  const getFallbackBackground = () => {
    switch (variant) {
      case "principal":
        return "bg-gradient-to-br from-amber-600 to-amber-800";
      case "family":
        return "bg-gradient-to-br from-rose-600 to-rose-800";
      case "organization":
        return "bg-gradient-to-br from-emerald-600 to-emerald-800";
      default:
        return "bg-gradient-to-br from-blue-600 to-blue-800";
    }
  };

  return (
      <div className="relative w-full max-w-[270px] h-[330px] rounded-2xl overflow-hidden shadow-2xl">
      {/* Fallback gradient background */}
      <div className={`absolute inset-0 ${getFallbackBackground()}`} />
      
      {/* Background image if available */}
      {person?.entity_image_url ? (
        <Image
          src={person.entity_image_url}
          alt={person.entity_display_name ?? "Profile image"}
          fill
          className="object-cover"
        />
      ) : null}
     

      {/* Progressive blur overlay */}
      <div 
        className="absolute inset-0 backdrop-blur-lg"
        style={{
          maskImage: 'linear-gradient(to bottom, transparent 0%, transparent 30%, rgba(0,0,0,0.3) 40%, rgba(0,0,0,1) 50%)',
          WebkitMaskImage: 'linear-gradient(to bottom, transparent 0%, transparent 30%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,1) 60%)'
        }}
      ></div>
      
      {/* Gradient overlay */}
      <div className={`absolute inset-0 bg-gradient-to-t ${getBackgroundGradient()}`}></div>

      {/* Top badges */}
      <div className="absolute top-4 left-4 flex items-center space-x-2">
        {variant === "principal" && (
          <div className="bg-gradient-to-r from-amber-400/90 to-yellow-500/90 backdrop-blur-md rounded-full px-3 py-1 flex items-center space-x-1 shadow-lg">
            <Crown className="h-4 w-4 text-white" />
            <span className="text-white text-xs font-medium">PRINCIPAL</span>
          </div>
        )}
        
        {variant === "family" && (
          <div
            className={`bg-gradient-to-r ${getRelationshipColor(person?.relationshipType ?? "other")} backdrop-blur-md rounded-full px-3 py-1 flex items-center space-x-1 shadow-lg`}
          >
            {getRelationshipIcon(person?.relationshipType ?? "other")}
            <span className="text-white text-xs font-medium uppercase">{person?.relationshipType}</span>
          </div>
        )}
        
        {variant === "organization" && (
          <div className="bg-gradient-to-r from-emerald-400/90 to-green-500/90 backdrop-blur-md rounded-full px-3 py-1 flex items-center space-x-1 shadow-lg">
            <Building className="h-4 w-4 text-white" />
            <span className="text-white text-xs font-medium">ORGANIZATION</span>
          </div>
        )}
        
        {variant === "professional" && (
          <div className="bg-blue-600/90 backdrop-blur-md rounded-full px-3 py-1 flex items-center space-x-1 shadow-lg">
            <Briefcase className="h-4 w-4 text-white" />
            <span className="text-white text-xs font-medium uppercase">PROFESSIONAL</span>
          </div>
        )}
      </div>

      {/* Photo icon - only show when no avatar */}
      {!person?.entity_image_url && (
        <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
          <PopoverTrigger asChild>
            <div
              className="absolute top-4 right-4 cursor-pointer"
              onClick={handleSearchClick}
            >
              <div className="bg-black/20 backdrop-blur-md rounded-full p-2 shadow-lg">
                <Camera className="h-5 w-5 text-white/70" />
              </div>
            </div>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            <div className="grid gap-4">
              <div className="space-y-2">
                <h4 className="font-medium leading-none">Select an Image</h4>
                <p className="text-sm">
                  Click an image to set it as the avatar.
                </p>
              </div>
              {isLoading ? (
                <div className="grid grid-cols-3 gap-2">
                  {Array.from({ length: 3 }, (_, index) => (
                    <Skeleton key={index} className="w-24 h-24 flex items-center justify-center">
                      <Spinner />
                    </Skeleton>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-3 gap-2">
                  {imageResults.map((image, index) => (
                    <button key={index} onClick={() => handleImageSelect(image.url)} className="rounded-md overflow-hidden">
                      <Image
                        src={image.url}
                        alt={`Search result ${index + 1}`}
                        width={100}
                        height={100}
                        className="object-cover h-full w-full"
                      />
                    </button>
                  ))}
                </div>
              )}
              {imageResults.length === 0 && !isLoading && (
                <p className="text-sm text-center text-muted-foreground">No images found.</p>
              )}
              <Button variant="secondary" onClick={() => setIsPopoverOpen(false)}>
                Cancel
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      )}

      {/* Content overlay on blurred section */}
      <div className="absolute bottom-0 left-0 right-0 h-2/3 px-4 pt-6 pb-2 flex flex-col justify-end">
        {/* Name */}
        <div className="mb-2">
          <div className="flex items-baseline space-x-2 mb-0">
            <h3 className="text-3xl font-bold text-white">{person?.entity_display_name ?? "Name"}</h3>
          </div>
          <p className={`${getAccentColor()} text-sm`}>
            {person?.entity_ai_short_description ?? "Description"}
          </p>
        </div>

        {/* Professional info or Employee count */}
        <div className="mb-2">
          {variant === "organization" ? (
            <p className="text-white font-medium text-base">
              {person?.details?.organization_employees_amount ?? 0} employees
            </p>
          ) : (
            <>
              <p className="text-white font-medium text-base">{person?.title ?? "Title"}</p>
              <p className={`${getAccentColor()} text-sm`}>
                {person?.company ?? "Company"}
              </p>
            </>
          )}
        </div>

        {/* Contact icons row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Email or Website */}
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => copyToClipboard(
                    variant === "organization"
                      ? person?.website ?? "https://example.com"
                      : person?.primary_email ?? "<EMAIL>"
                  )}
                  className={`p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors`}
                >
                  {variant === "organization" ? (
                    <Globe className={`h-4 w-4 ${getAccentColor()}`} />
                  ) : (
                    <Mail className={`h-4 w-4 ${getAccentColor()}`} />
                  )}
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{
                  variant === "organization"
                    ? person?.website ?? "https://example.com"
                    : person?.primary_email ?? "<EMAIL>"
                }</p>
              </TooltipContent>
            </Tooltip>

            {/* Phone */}
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => copyToClipboard(person?.primary_phone ?? "+****************")}
                  className={`p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors`}
                >
                  <Phone className={`h-4 w-4 ${getAccentColor()}`} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{person?.primary_phone ?? "+****************"}</p>
              </TooltipContent>
            </Tooltip>

            {/* Location */}
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => copyToClipboard(person?.primary_address ?? "Location")}
                  className={`p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors`}
                >
                  <MapPin className={`h-4 w-4 ${getAccentColor()}`} />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{person?.primary_address ?? "Location"}</p>
              </TooltipContent>
            </Tooltip>

            {/* Birthday - only show for non-organization variants */}
            {person?.birthday && variant !== "organization" ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => copyToClipboard(person.birthday ? person.birthday.replace(/, \d{4}/, '') : '')}
                    className={`p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors`}
                  >
                    <Cake className={`h-4 w-4 ${getAccentColor()}`} />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{person.birthday ? person.birthday.replace(/, \d{4}/, '') : ''}</p>
                </TooltipContent>
              </Tooltip>
            ) : null}
          </div>

          {/* Relationships count - show on all variants */}
          {person?.familyMemberCount && (
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="flex items-center space-x-1 px-3 py-1 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors">
                  <Users className={`h-4 w-4 ${getAccentColor()}`} />
                  <span className={`text-sm font-medium ${getAccentColor()}`}>
                    {person.familyMemberCount}
                  </span>
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{person.familyMemberCount} Relationships</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
}

// Export the DirectoryCardLarge component
export default DirectoryCardLarge;
