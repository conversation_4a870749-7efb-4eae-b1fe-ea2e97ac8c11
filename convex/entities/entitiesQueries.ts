// convex/entities/entitiesQueries.ts
import { query } from "../_generated/server";
import { v } from "convex/values";
import { getAuthenticatedOrgContext } from "../auth";

export const getAllEntities = query({
  args: {},
  returns: v.array(
    v.object({
      _id: v.id("entities"),
      _creationTime: v.number(),
      clerkTenantId: v.string(),
      entity_type: v.union(v.literal("person"), v.literal("organization")),
      entity_display_name: v.string(),
      primary_email: v.optional(v.string()),
      primary_phone: v.optional(v.string()),
      primary_address: v.optional(v.string()),
      entity_image_url: v.optional(v.string()),
      entity_links: v.optional(
        v.array(
          v.object({
            type: v.union(
              v.literal("linkedin"),
              v.literal("domain_name"),
              v.literal("socialmedia")
            ),
            url: v.string(),
          })
        )
      ),
      entity_ai_short_description: v.optional(v.string()),
      entity_ai_summary: v.optional(v.string()),
      entity_last_activity: v.optional(v.number()),
    })
  ),
  handler: async (ctx) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);

    // Get all entities for this tenant, sorted by creation time (most recent first)
    const entities = await ctx.db
      .query("entities")
      .withIndex("by_clerkTenantId", (q) => 
        q.eq("clerkTenantId", clerkTenantId)
      )
      .order("desc") // Most recent first
      .collect();

    return entities;
  },
});

export const searchEntities = query({
  args: {
    searchTerm: v.string(),
    excludeIds: v.optional(v.array(v.id("entities"))), // IDs to exclude from results
  },
  returns: v.array(
    v.object({
      id: v.id("entities"),
      name: v.string(),
      type: v.union(v.literal("person"), v.literal("organization")),
      subtext: v.string(),
    })
  ),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);

    if (!args.searchTerm.trim()) {
      return [];
    }

    // Search entities by display name
    const entities = await ctx.db
      .query("entities")
      .withIndex("by_clerkTenantId_entity_type", (q) => 
        q.eq("clerkTenantId", clerkTenantId)
      )
      .filter((q) => 
        q.or(
          q.eq(q.field("entity_display_name"), args.searchTerm),
          // Simple prefix search - could be enhanced with full-text search later
          q.gte(q.field("entity_display_name"), args.searchTerm)
        )
      )
      .take(20); // Limit results

    // Filter out excluded IDs and format for wizard
    const filteredEntities = entities
      .filter(entity => !args.excludeIds?.includes(entity._id))
      .map(entity => ({
        id: entity._id,
        name: entity.entity_display_name,
        type: entity.entity_type,
        subtext: entity.primary_email || entity.entity_type === "person" ? "Contact" : "Organization",
      }));

    return filteredEntities;
  },
});