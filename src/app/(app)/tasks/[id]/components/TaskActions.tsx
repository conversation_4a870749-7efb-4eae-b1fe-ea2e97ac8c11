import { <PERSON>, <PERSON>Header, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import type { Priority, Status } from "../../types";
import { statusEnum, priorityEnum } from "../../constants";
import { api } from "@/../convex/_generated/api";
import { useMutation, useQuery } from "convex/react";
import { formatPriority, formatStatus } from "@/lib/utils";
import { toast } from "sonner";
export function TaskActions({ taskId }: { taskId: string }) {
  const users = useQuery(api.userProfiles.userProfilesQueries.getUsers);

  const updateTask = useMutation(api.tasks.tasksMutations.updateTask);

  const handleStatusChange = async (status: Status) => {
    try {
      await updateTask({ status, taskId });
      toast.success("Status updated");
    } catch (error) {
      toast.error("Failed to update status");
    }
  };

  const handlePriorityChange = async (priority: Priority) => {
    try {
      await updateTask({ priority, taskId });
      toast.success("Priority updated");
    } catch (error) {
      toast.error("Failed to update priority");
    }
  };

  const handleReassignTask = async (assigneeId: string) => {
    try {
      await updateTask({ assigneeId, taskId });
      toast.success("Task re-assigned");
    } catch (error) {
      toast.error("Failed to re-assign task");
    }
  };

  return (
    <Card className="mt-6 border border-white/20 bg-white/80 shadow-sm backdrop-blur-sm">
      <CardHeader className="pb-3">
        <h2 className="text-lg font-semibold text-slate-800">Actions</h2>
      </CardHeader>
      <CardContent className="space-y-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="w-full justify-start"
              variant="outline"
              size="sm"
            >
              Change status
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[200px]">
            {Object.values(statusEnum).map((status) => (
              <DropdownMenuItem
                key={status}
                onClick={() => handleStatusChange(status)}
              >
                {formatStatus(status)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="w-full justify-start"
              variant="outline"
              size="sm"
            >
              Change priority
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[200px]">
            {Object.values(priorityEnum).map((priority) => (
              <DropdownMenuItem
                key={priority}
                onClick={() => handlePriorityChange(priority)}
              >
                {formatPriority(priority)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="w-full justify-start"
              variant="outline"
              size="sm"
            >
              Reassign task
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[200px]">
            {users?.map((user) => (
              <DropdownMenuItem
                key={user._id}
                className="flex items-center gap-2"
                onClick={() => handleReassignTask(user._id)}
              >
                <Avatar className="h-6 w-6">
                  <AvatarImage
                    src={user.imageUrl ?? "/placeholder.svg"}
                    alt={`${user.firstName} ${user.lastName}`}
                  />
                  <AvatarFallback>
                    {user.firstName[0]}
                    {user.lastName[0]}
                  </AvatarFallback>
                </Avatar>
                <span>
                  {user.firstName} {user.lastName}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </CardContent>
    </Card>
  );
}
