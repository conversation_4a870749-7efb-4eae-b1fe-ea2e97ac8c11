// src/app/(app)/directory/components/AddEntityWizard/components/RelationshipSearch.tsx

import React, { useRef, useEffect } from 'react';
import { Search, Plus, User, Building2, Loader2 } from 'lucide-react';
import type { SearchResult } from '../AddEntityWizard.types';

interface RelationshipSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  searchResults: SearchResult[];
  isSearching: boolean;
  onSelectResult: (result: SearchResult) => void;
  onCreateNew: () => void;
  showResults: boolean;
  onShowResultsChange: (show: boolean) => void;
}

export const RelationshipSearch: React.FC<RelationshipSearchProps> = ({
  searchQuery,
  onSearchChange,
  searchResults,
  isSearching,
  onSelectResult,
  onCreateNew,
  showResults,
  onShowResultsChange
}) => {
  const searchRef = useRef<HTMLDivElement>(null);

  // Handle clicking outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        onShowResultsChange(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onShowResultsChange]);

  return (
    <div ref={searchRef} className="relative mb-6">
      <div className="relative">
        <Search size={20} className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => {
            onSearchChange(e.target.value);
            onShowResultsChange(e.target.value.length > 0);
          }}
          className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Search or create connections..."
        />
        {isSearching && (
          <Loader2 size={20} className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 animate-spin" />
        )}
      </div>

      {showResults && searchQuery && (
        <div className="absolute z-10 mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto">
          {searchResults.length > 0 ? (
            <>
              {searchResults.map((result) => (
                <button
                  key={result.id}
                  onClick={() => onSelectResult(result)}
                  className="w-full px-4 py-3 hover:bg-gray-50 flex items-center gap-3 text-left transition-colors"
                >
                  <div className={`
                    w-10 h-10 rounded-lg flex items-center justify-center
                    ${result.type === 'person' ? 'bg-blue-100 text-blue-600' : 'bg-purple-100 text-purple-600'}
                  `}>
                    {result.type === 'person' ? <User size={20} /> : <Building2 size={20} />}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{result.name}</div>
                    <div className="text-sm text-gray-500">{result.subtext}</div>
                  </div>
                </button>
              ))}
            </>
          ) : !isSearching ? (
            <div className="px-4 py-8 text-center text-gray-500">
              <p className="text-sm">No results found</p>
            </div>
          ) : null}
          
          {searchQuery && !isSearching && (
            <button
              onClick={onCreateNew}
              className="w-full px-4 py-3 hover:bg-blue-50 flex items-center gap-3 text-left text-blue-600 border-t transition-colors"
            >
              <Plus size={20} />
              <span>Create &quot;{searchQuery}&quot;</span>
            </button>
          )}
        </div>
      )}
    </div>
  );
};