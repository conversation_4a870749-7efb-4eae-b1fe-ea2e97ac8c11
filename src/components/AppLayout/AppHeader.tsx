"use client";

import React, { useEffect, useState } from "react";
import { OrganizationSwitcher, UserButton } from "@clerk/nextjs";
import { AppHeaderBreadcrumb } from "./AppHeaderBreadcrumb";
import { DarkModeToggle } from "./DarkModeToggle";

export function AppHeader() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <header className="fixed z-20 top-0 right-0 left-0 h-10 appheader-bottom-border  shadow-blue-950/40 backdrop-saturate-110 backdrop-blur-md">
      <div className="flex h-full items-center justify-between px-6">
        {/* Logo */}

        <AppHeaderBreadcrumb />

        {/* User Profile */}
        <div className="flex items-center">
        <DarkModeToggle />
          {isMounted && (
            <OrganizationSwitcher
              hidePersonal={true}
              afterCreateOrganizationUrl="/home"
            />
          )}
          {isMounted && <UserButton />}
        </div>
      </div>
    </header>
  );
}
