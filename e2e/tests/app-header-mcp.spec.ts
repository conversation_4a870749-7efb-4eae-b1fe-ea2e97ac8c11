import { test, expect } from '@playwright/test';

/**
 * AppHeader Component Tests - Generated with <PERSON><PERSON> MCP
 * Tests comprehensive functionality of the AppHeader component including:
 * - User avatar and profile functionality
 * - Organization switcher
 * - Breadcrumb navigation
 * - Responsive design
 * - Clerk authentication integration
 */

test.describe('AppHeader Component - MCP Generated Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app and ensure authentication
    await page.goto('/');
    
    // Wait for authentication and navigation to home page
    await page.waitForURL('**/home');
    
    // Wait for header to be fully loaded
    await page.waitForSelector('header', { timeout: 10000 });
    
    // Close CopilotKit sidebar if it's visible by pressing the Escape key
    const copilotSidebar = page.locator('.copilotKitWindow.open, [aria-label*="CopilotKit"]');
    if (await copilotSidebar.isVisible()) {
      await page.keyboard.press('Escape');
      await page.waitForTimeout(1000); // Wait for sidebar to close completely
    }
  });

  test('should handle user menu and organization switcher flows', async ({ page }) => {
    // 1. Interact with user avatar dropdown
    const userButton = page.locator('.cl-userButton-root, [data-clerk-element="userButton"]').first();
    await expect(userButton, 'User button should be visible').toBeVisible();
    await userButton.click({ force: true });

    const dropdown = page.locator('.cl-userButtonPopoverCard');
    await expect(dropdown, 'User menu dropdown should appear').toBeVisible();

    // 2. Verify dropdown contents and open "Manage account"
    const manageAccountItem = dropdown.locator('[role="menuitem"]', { hasText: /manage account/i });
    await expect(manageAccountItem, '"Manage account" option should be in the dropdown').toBeVisible();

    const signOutItem = dropdown.locator('[role="menuitem"]', { hasText: /sign out/i });
    await expect(signOutItem, '"Sign out" option should be in the dropdown').toBeVisible();

    await manageAccountItem.click();

    const modal = page.locator('.cl-userProfile-root');
    await expect(modal, 'Profile modal should open').toBeVisible({ timeout: 10000 });
    await expect(modal.locator('.cl-headerTitle'), 'Modal title should be "Profile"').toContainText('Profile');

    // 3. Close profile modal
    const closeButton = modal.locator('button[aria-label*="Close"]');
    await expect(closeButton, 'Modal should have a close button').toBeVisible();
    await closeButton.click();
    await expect(modal, 'Profile modal should close').not.toBeVisible();

    // 4. Interact with organization switcher (if it exists)
    const orgSwitcher = page.locator('.cl-organizationSwitcher-root, [data-clerk-element="organizationSwitcher"]').first();
    if (await orgSwitcher.count() > 0) {
      await expect(orgSwitcher, 'Organization switcher should be visible').toBeVisible();
      await orgSwitcher.click();

      const orgPopup = page.locator('[role="menu"]:has-text("Create organization")');
      await expect(orgPopup, 'Organization switcher popup should appear').toBeVisible();

      // Also verify that the popup contains at least one organization item
      const firstOrgItem = orgPopup.locator('[role="menuitem"]').first();
      await expect(firstOrgItem, 'Popup should contain at least one organization').toBeVisible();
    } else {
      // If the switcher doesn't exist, it's likely because the user is in only one org.
      // We can log this and continue, making the test more robust.
      console.log('Organization switcher not found, skipping interaction test.');
    }
  });

  test('should display breadcrumb navigation correctly', async ({ page }) => {
    // Navigate to different pages to test breadcrumbs
    await page.goto('/tasks');
    await page.waitForLoadState('networkidle');
    
    // Check breadcrumb container
    const breadcrumb = page.locator('[aria-label="breadcrumb"], nav:has(.breadcrumb)').first();
    await expect(breadcrumb).toBeVisible();

    // Verify "Home" breadcrumb link is present
    const homeLink = breadcrumb.locator('a', { hasText: 'Home' });
    await expect(homeLink).toBeVisible();
    await expect(homeLink).toHaveAttribute('href', '/home');
    
    // Verify "Tasks" breadcrumb item is the current page
    const tasksItem = breadcrumb.locator('span', { hasText: 'Tasks' });
    await expect(tasksItem).toBeVisible();
    await expect(tasksItem).toHaveAttribute('aria-current', 'page');
  });

  test('should handle responsive design correctly', async ({ page }) => {
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    
    const header = page.locator('header');
    await expect(header).toBeVisible();
    
    // Verify header styling classes for desktop
    await expect(header).toHaveClass(/backdrop-blur/);
    await expect(header).toHaveClass(/fixed/);
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(header).toBeVisible();
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(header).toBeVisible();
    
    // On mobile, check if breadcrumb items are responsive
    const breadcrumbItems = page.locator('[aria-label="breadcrumb"] li, [aria-label="breadcrumb"] > *');
    if ((await breadcrumbItems.count()) > 0) {
      // Just verify breadcrumb is still present on mobile, specific responsive behavior may vary
      await expect(breadcrumbItems.first()).toBeVisible();
    }
  });

  test('should maintain header layout and styling', async ({ page }) => {
    const header = page.locator('header');
    
    // Verify header positioning
    await expect(header).toHaveClass(/fixed/);
    await expect(header).toHaveClass(/top-0/);
    
    // Check backdrop blur effect
    await expect(header).toHaveClass(/backdrop-blur/);
    
    // Verify header height is reasonable
    const headerBox = await header.boundingBox();
    expect(headerBox?.height).toBeGreaterThan(35);
    expect(headerBox?.height).toBeLessThan(100);
    
    // Check header content layout
    const headerContent = header.locator('> div').first();
    await expect(headerContent).toHaveClass(/flex/);
    await expect(headerContent).toHaveClass(/justify-between/);
  });

  test('should verify accessibility compliance', async ({ page }) => {
    // Check header has proper ARIA labels and roles
    const header = page.locator('header');
    
    // Verify banner role (implicit for header element)
    await expect(header).toBeVisible();
    
    // Check avatar button accessibility
    const avatarButton = page.locator('header button').filter({ 
      has: page.locator('img, [role="img"], .cl-avatar') 
    }).first();
    
    // Avatar button should be focusable
    await avatarButton.focus();
    await expect(avatarButton).toBeFocused();
    
    // Breadcrumb should have proper navigation role
    const breadcrumb = page.locator('[aria-label="breadcrumb"], nav').first();
    if (await breadcrumb.isVisible()) {
      await expect(breadcrumb).toHaveAttribute('aria-label', /breadcrumb/i);
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Test header behavior when user data might be loading
    await page.goto('/');
    
    // Header should still be visible even during loading states
    const header = page.locator('header');
    await expect(header).toBeVisible();
    
    // Test behavior with slow network (simulate)
    await page.route('**/*', route => {
      setTimeout(() => route.continue(), 100);
    });
    
    await page.reload();
    
    // Header should remain stable
    await expect(header).toBeVisible();
  });
});