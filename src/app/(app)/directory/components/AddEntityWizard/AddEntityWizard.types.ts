// src/components/entities/AddEntityWizard/AddEntityWizard.types.ts

import type { Id } from "@/../convex/_generated/dataModel";
import type { LucideIcon } from 'lucide-react';

export type EntityType = 'person' | 'organization';

export type PersonCategory = 
  | 'client' 
  | 'client_family' 
  | 'potential_client' 
  | 'service_provider' 
  | 'business_contact';

export type OrganizationCategory = 
  | 'client' 
  | 'service_provider';

export interface EntityFormData {
  // Person fields
  person_first_name: string;
  person_last_name: string;
  person_category: PersonCategory | '';
  person_birthday: string;
  person_notes: string;
  
  // Organization fields
  organization_name: string;
  organization_category: OrganizationCategory | '';
  organization_industry: string;
  organization_employees_amount: string;
  organization_annual_revenue: string;
  organization_notes: string;
  
  // Shared fields
  client_type: 'individual' | 'organization' | '';
  primary_email: string;
  primary_phone: string;
  primary_address: string;
  entity_links: EntityLink[];
  entity_image_url?: string;
}

export interface EntityLink {
  type: 'linkedin' | 'domain_name' | 'socialmedia';
  url: string;
}

export interface CategoryOption<T extends string> {
  id: T;
  label: string;
  icon: LucideIcon;
  color: string;
  description?: string;
}

export interface SearchResult {
  id: Id<"entities"> | string; // Allow temporary string IDs for new entities
  name: string;
  type: 'person' | 'organization';
  subtext: string;
}

export interface Relationship {
  id: Id<"entities"> | string; // Allow temporary string IDs
  entity: SearchResult;
  relationshipType: string;
}

export interface WizardStep {
  number: number;
  title: string;
  icon: LucideIcon;
}

export interface StepProps {
  formData: EntityFormData;
  onUpdate: (updates: Partial<EntityFormData>) => void;
  onNext?: () => void;
  onBack?: () => void;
}

export interface PersonStepProps extends StepProps {
  personCategory: PersonCategory | '';
}

export interface OrganizationStepProps extends StepProps {
  organizationCategory: OrganizationCategory | '';
}

export interface ConnectionsStepProps extends StepProps {
  entityType: EntityType;
  entityCategory: PersonCategory | OrganizationCategory | '';
  relationships: Relationship[];
  onAddRelationship: (entity: SearchResult) => void;
  onRemoveRelationship: (id: Id<"entities"> | string) => void;
}

export interface SuggestionItem {
  type: 'person' | 'organization';
  suggestion: string;
}

export type FormErrors = Record<string, string>;
