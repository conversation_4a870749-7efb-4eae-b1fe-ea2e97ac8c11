import { <PERSON>, <PERSON>Header, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { priorityColors } from "../../constants";
import { statusConfig } from "../../constants";
import { formatStatus } from "@/lib/utils";
import type { Status, Priority } from "../../types";

interface Assignee {
  name: string;
  avatar?: string;
}

interface TaskDetailsSidebarProps {
  status: Status;
  priority: Priority;
  assignee: Assignee;
}

export function TaskDetailsSidebar({
  status,
  priority,
  assignee,
}: TaskDetailsSidebarProps) {
  return (
    <Card className="border border-white/20 bg-white/80 shadow-sm backdrop-blur-sm">
      <CardHeader className="pb-3">
        <h2 className="text-lg font-semibold text-slate-800">Details</h2>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="mb-1 text-sm font-medium text-slate-700">Status</p>
          <Badge className={statusConfig[status].color}>
            {formatStatus(status)}
          </Badge>
        </div>
        <div>
          <p className="mb-1 text-sm font-medium text-slate-700">Priority</p>
          <div className="flex items-center gap-2">
            <Badge className={priorityColors[priority]}>
              {priority.charAt(0).toUpperCase() + priority.slice(1)}
            </Badge>
          </div>
        </div>
        <Separator />
        <div>
          <p className="mb-2 text-sm font-medium text-slate-700">Assignee</p>
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarImage
                src={assignee.avatar ?? "/placeholder.svg"}
                alt={assignee.name}
              />
              <AvatarFallback>{assignee.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <span className="text-sm text-slate-600">{assignee.name}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
