/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth_clerkWebhooks from "../auth/clerkWebhooks.js";
import type * as auth from "../auth.js";
import type * as comments_commentsMutations from "../comments/commentsMutations.js";
import type * as comments_commentsQueries from "../comments/commentsQueries.js";
import type * as entities_entitiesMutations from "../entities/entitiesMutations.js";
import type * as entities_entitiesQueries from "../entities/entitiesQueries.js";
import type * as http from "../http.js";
import type * as identity from "../identity.js";
import type * as serp from "../serp.js";
import type * as tasks_tasksMutations from "../tasks/tasksMutations.js";
import type * as tasks_tasksQueries from "../tasks/tasksQueries.js";
import type * as tavily from "../tavily.js";
import type * as tenants_tenantMutations from "../tenants/tenantMutations.js";
import type * as types from "../types.js";
import type * as userProfiles_userProfilesMutations from "../userProfiles/userProfilesMutations.js";
import type * as userProfiles_userProfilesQueries from "../userProfiles/userProfilesQueries.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "auth/clerkWebhooks": typeof auth_clerkWebhooks;
  auth: typeof auth;
  "comments/commentsMutations": typeof comments_commentsMutations;
  "comments/commentsQueries": typeof comments_commentsQueries;
  "entities/entitiesMutations": typeof entities_entitiesMutations;
  "entities/entitiesQueries": typeof entities_entitiesQueries;
  http: typeof http;
  identity: typeof identity;
  serp: typeof serp;
  "tasks/tasksMutations": typeof tasks_tasksMutations;
  "tasks/tasksQueries": typeof tasks_tasksQueries;
  tavily: typeof tavily;
  "tenants/tenantMutations": typeof tenants_tenantMutations;
  types: typeof types;
  "userProfiles/userProfilesMutations": typeof userProfiles_userProfilesMutations;
  "userProfiles/userProfilesQueries": typeof userProfiles_userProfilesQueries;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
