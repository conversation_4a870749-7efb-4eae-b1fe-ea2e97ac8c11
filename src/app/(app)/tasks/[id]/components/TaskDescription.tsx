import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Content } from "@/components/ui/card";

interface TaskDescriptionProps {
  description: string;
}

export function TaskDescription({ description }: TaskDescriptionProps) {
  return (
    <Card className="border border-white/20 bg-white/80 shadow-sm backdrop-blur-sm">
      <CardHeader className="pb-3">
        <h2 className="text-lg font-semibold text-slate-800">Description</h2>
      </CardHeader>
      <CardContent>
        <p className="text-sm whitespace-pre-wrap text-slate-600">
          {description}
        </p>
      </CardContent>
    </Card>
  );
}
