Create Playwright tests for AppHeader user interaction flows:
    
    1. User Profile Flow:
       - Click avatar button
       - Verify dropdown menu appears
       - Click "Profile" option
       - Verify Clerk UserProfile modal opens
       
    2. Organization Switcher Flow:
       - Locate organization switcher component
       - Test organization switching functionality
       - Verify proper organization context updates
       
    3. Navigation Flow:
       - Test breadcrumb navigation
       - Verify proper page titles and navigation states
       
    Generate these as separate test cases with proper setup and teardown.