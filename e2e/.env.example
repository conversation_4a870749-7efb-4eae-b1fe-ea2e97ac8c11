# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here
NEXT_PUBLIC_CLERK_FRONTEND_API_URL=https://your-clerk-frontend-api.clerk.accounts.dev

# Application URLs
PLAYWRIGHT_BASE_URL=http://localhost:3000
PLAYWRIGHT_HEADED=false

# MCP Server Configuration
PLAYWRIGHT_MCP_ENABLED=true

# Test User Credentials (create a dedicated test user for E2E tests)
E2E_CLERK_USER_USERNAME=<EMAIL>
E2E_CLERK_USER_PASSWORD=your-test-password