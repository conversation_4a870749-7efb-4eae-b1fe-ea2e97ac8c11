# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `pnpm dev` - Start development server with Turbo
- `pnpm build` - Build production application
- `pnpm start` - Start production server
- `pnpm preview` - Build and start production server

### Code Quality
- `pnpm lint` - Run ESLint on Next.js and Convex code
- `pnpm lint:fix` - Auto-fix linting issues
- `pnpm typecheck` - Run TypeScript compiler without emitting files
- `pnpm check` - Run both linting and type checking
- `pnpm format:check` - Check code formatting with Prettier
- `pnpm format:write` - Auto-format code with Prettier

### Package Management
- **Always use `pnpm`** as the package manager (specified in package.json)

## Architecture Overview

### Tech Stack
- **Framework**: Next.js 15 with React 19
- **Database**: Convex.dev (real-time database with built-in auth)
- **Authentication**: Clerk (with organization support)
- **Styling**: Tailwind CSS with ShadCN UI components
- **AI Integration**: CopilotKit with Vercel AI SDK
- **Type Safety**: TypeScript with strict validation

### Multi-Tenant Architecture
The application uses a **tenant-based isolation model**:

- **Tenants Table**: Organizations (mapped to Clerk organizations)
- **User Profiles**: Users belong to tenants via `activeTenantId`
- **Data Isolation**: All business data (tasks, comments) is scoped by `tenantId`
- **Authentication Flow**: Users must have both Clerk auth AND tenant membership

### Key Data Models

#### User & Tenant Relationship
```typescript
// Users can switch between organizations
userProfiles: {
  clerkUserId: string,           // Clerk user ID
  activeTenantId: Id<"tenants">, // Current active tenant
  // ... profile fields
}

tenants: {
  clerkOrganizationId: string,   // Maps to Clerk organization
  name: string,
  // ... tenant fields
}
```

#### Task Management
```typescript
tasks: {
  tenantId: Id<"tenants">,       // Strict tenant isolation
  creatorId: string,             // User who created task
  assigneeId: string,            // User assigned to task
  // ... task fields
}
```

### Authentication & Authorization

#### Middleware Flow (`src/middleware.ts`)
1. **Public routes**: `/login`, `/sign-up` (no auth required)
2. **After-auth routes**: User onboarding flow
3. **Protected routes**: Require both Clerk auth AND tenant membership
4. **Automatic redirect**: `/` → `/tasks` for authenticated users

#### Convex Authentication Pattern
All protected Convex functions follow this pattern:
```typescript
const identity = await ctx.auth.getUserIdentity();
if (!identity) {
  throw new ConvexError("User is not authenticated.");
}
const user = await ctx.runQuery(api.userProfiles.userProfilesQueries.getUserByTokenIdentifier, {
  tokenIdentifier: identity.tokenIdentifier,
});
if (!user) {
  throw new ConvexError("User not found.");
}
// Verify tenant access for tenant-specific operations
```

### File Structure

#### Convex Backend (`convex/`)
- **Organized by feature**: `tasks/`, `userProfiles/`, `tenants/`, etc.
- **Three function types**: queries (read), mutations (write), actions (Node.js runtime)
- **Strict validation**: All functions have input/output validators
- **Database indexes**: Optimized for tenant-scoped queries

#### Frontend (`src/`)
- **App Router**: Route groups `(app)/` and `(auth)/`
- **Component organization**: Feature-based in `src/app/(app)/[feature]/components/`
- **Shared components**: `src/components/ui/` (ShadCN), `src/components/AppLayout/`
- **Layouts**: Nested layouts for different app sections

### Important Patterns

#### Convex Function Validation
All Convex functions must have explicit return validators:
```typescript
export const myMutation = mutation({
  args: { /* input validation */ },
  returns: v.null(), // or v.id("table"), v.object({...}), etc.
  handler: async (ctx, args) => { /* implementation */ }
});
```

#### Error Handling
- **Use ConvexError**: `throw new ConvexError("message")` not `throw new Error()`
- **Type errors properly**: `catch (error: unknown)`
- **Validate early**: Check inputs at function boundaries

#### Database Best Practices
- **Avoid `.filter()` on queries**: Use indexes and query constraints
- **Use `.collect()` sparingly**: Only for small result sets, prefer pagination
- **Await all promises**: Including `ctx.scheduler.runAfter`, `ctx.db.patch`
- **Proper indexing**: All tenant-scoped queries use `by_tenantId` indexes

### Environment Configuration

#### Required Environment Variables
- `NEXT_PUBLIC_CONVEX_URL` - Convex deployment URL
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk public key
- `CLERK_SECRET_KEY` - Clerk secret key (server-side)
- `NEXT_PUBLIC_CLERK_FRONTEND_API_URL` - Clerk frontend API
- `NEXT_PUBLIC_COPILOT_CLOUD_PUBLIC_API_KEY` - CopilotKit API key

#### Environment Validation
Uses `@t3-oss/env-nextjs` for type-safe environment variables in `src/env.js`.

### Development Guidelines

#### Comments & Documentation
- Never delete old comments unless obviously wrong
- Document WHY, not WHAT in code comments
- Use clear, short sentences in comments

#### TypeScript
- Avoid `any` type unless absolutely necessary
- Leverage TypeScript inference
- Use proper return types on all functions

#### Database Operations
- All business data must be tenant-scoped
- Use proper authentication checks on all mutations/queries
- Convex automatically provides `_id` and `_creationTime` fields

### AI Integration

#### CopilotKit Setup
- Integrated in app layout with custom window component
- Configured for task management assistance
- Uses glassmorphism styling effects

The application prioritizes type safety, multi-tenant data isolation, and real-time capabilities through Convex's reactive database system.