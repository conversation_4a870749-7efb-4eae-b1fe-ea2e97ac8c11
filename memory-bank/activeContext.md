# Active Context

## Current Work Focus
The primary focus has been a massive simplification of the authentication and organization management systems by moving to a pure, Clerk-native architecture. This eliminates previous complexities, custom logic, and potential race conditions.

## Recent Changes
- **Implemented Clerk-Native Auth:** Ripped out all custom authentication and organization-syncing logic.
- **Introduced Middleware Routing:** Added a simple and clean `middleware.ts` file that handles all routing based on the user's authentication state (`userId` and `orgId` in the Clerk JWT).
- **Eliminated Sync Components:** Deleted the `SyncActiveOrg.tsx` component and associated backend logic (`createTenantAndUpdateUser`), which were responsible for a complex and fragile client-side orchestration flow.
- **Simplified Org Management:** All organization creation, selection, and invitation handling is now managed by Clerk's `<OrganizationList>` component on the `/org-selection` page.
- **Direct Data Scoping:** All Convex queries are now directly scoped to the active organization using the `clerkTenantId` from the JWT, ensuring robust data isolation without extra logic.
- **Simplified File Structure:** Removed obsolete pages like `/after-auth`, `/org-invites`, and `/create-org`.

## Next Steps
- Build out core application features (Tasks, Projects, etc.) on top of the new, stable, and simplified authentication foundation.
- Ensure all new Convex queries and mutations continue to use the `clerkTenantId` for data scoping.
- Enhance the `AddEntityWizard` with more features like relationship editing and custom fields.

## Active Decisions and Considerations
- **Trust Clerk as the Source of Truth:** The system now fully relies on Clerk for user and organization state. The JWT is the ground truth, and there is no secondary state to manage or sync in our database.
- **Middleware for Control Flow:** All authentication-related control flow and redirection logic is centralized in the Next.js middleware, keeping page components clean and focused on their primary purpose.
- **Leverage Clerk Components Directly:** We are using Clerk's battle-tested UI components (`<SignInButton>`, `<OrganizationList>`) directly, reducing the amount of custom UI code we need to build and maintain.

## Learnings and Project Insights
- **Simplicity Wins:** The previous custom sync logic was complex and prone to errors. The new Clerk-native approach is simpler, more robust, and easier to reason about.
- **Centralized Routing is Cleaner:** Using middleware to handle auth redirects simplifies the overall architecture and removes the need for conditional rendering or redirects within multiple components.
- **Strong Typing is Key:** Augmenting the Convex `UserIdentity` to include `clerkTenantId` provides end-to-end type safety, from the JWT to the database query, eliminating a whole class of potential bugs.
- **Component-Based Wizards:** The `AddEntityWizard` demonstrates how a complex, multi-step form can be broken down into manageable, reusable components and hooks, improving maintainability and testability.
