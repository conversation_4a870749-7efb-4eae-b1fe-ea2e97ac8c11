# AddEntityWizard Feature

This document provides a conceptual overview of the `AddEntityWizard` feature, its components, and the user flow.

## Feature Diagram

```mermaid
graph TD
    subgraph "File Roles"
        direction LR
        Wizard[index.tsx] -- Manages State & Flow --> Steps
        Constants[AddEntityWizard.constants.ts] -- Defines Steps & Data --> Wizard
        Types[AddEntityWizard.types.ts] -- Defines Data Structures --> Wizard
        useEntityForm[hooks/useEntityForm.ts] -- Manages Form Logic --> Wizard
        StepComponents[steps/*.tsx] -- Render Individual Steps --> Steps
    end

    subgraph "User Flow"
        A[Start: PersonCategoryStep] --> B{Client or Potential?};
        B -- Yes --> C[ClientTypeStep];
        B -- No --> D{Person or Org?};
        
        C --> E{Individual or Org?};
        E -- Individual --> F[PersonInfoStep];
        E -- Organization --> G[OrganizationInfoStep];
        
        D -- Person --> F;
        D -- Organization --> G;
        
        F --> H[ConnectionsStep];
        G --> H;
        
        H --> I[Submit];
    end

    subgraph "Data Handling"
        direction LR
        I[Submit] -- Cleansed Data --> Backend[onComplete -> Convex];
    end

    classDef file fill:#f9f9f9,stroke:#333,stroke-width:2px;
    class Wizard,Constants,Types,useEntityForm,StepComponents file;
```

### How to Read the Diagram:

1.  **File Roles:** This section shows the main files involved in the `AddEntityWizard` and their responsibilities. `index.tsx` is the central orchestrator, while the other files provide support for constants, types, form logic, and UI.

2.  **User Flow:** This section maps out the user's journey through the wizard.
    *   It starts with the **`PersonCategoryStep`**.
    *   If the user selects "Client" or "Potential Client," they are taken to the **`ClientTypeStep`**. Otherwise, the wizard proceeds based on the default person/organization flow.
    *   The **`ClientTypeStep`** determines whether the subsequent steps will be for a person or an organization.
    *   The flow then converges on the **`ConnectionsStep`** before the final submission.

3.  **Data Handling:** This section illustrates how the data is processed.
    *   Depending on the path taken, either "Person Data" or "Org Data" is collected.
    *   The **`Submit`** step (handled by the `handleSubmit` function in `index.tsx`) cleanses the data, removing any irrelevant fields.
    *   The final, cleansed data is then sent to the Convex backend via the `onComplete` callback.
