{"name": "caliber", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint && npx eslint \"convex/**/*.{ts,tsx}\"", "lint:fix": "next lint --fix && npx eslint \"convex/**/*.{ts,tsx}\" --fix", "preview": "next build && next start", "start": "next start", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:e2e:codegen": "playwright codegen localhost:3000", "typecheck": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.19.5", "@convex-dev/eslint-plugin": "0.0.1-alpha.4", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.12.0", "@tavily/core": "^0.5.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.0", "convex-helpers": "^0.1.95", "lucide-react": "^0.511.0", "motion": "^12.18.1", "next": "^15.2.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "sonner": "^2.0.3", "svix": "^1.65.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.8"}, "devDependencies": {"@clerk/testing": "^1.9.0", "@eslint/eslintrc": "^3.3.1", "@executeautomation/playwright-mcp-server": "^1.0.6", "@playwright/mcp": "^0.0.29", "@playwright/test": "^1.53.1", "@stagewise-plugins/react": "^0.4.7", "@stagewise/toolbar-next": "^0.4.5", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "dotenv": "^17.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.3.0", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@8.15.4"}