// src/components/entities/AddEntityWizard/components/WizardProgress.tsx

import React from 'react';
import { Check, ChevronRight } from 'lucide-react';
import type { WizardStep } from '../AddEntityWizard.types';

interface WizardProgressProps {
  steps: WizardStep[];
  currentStep: number;
}

export const WizardProgress: React.FC<WizardProgressProps> = ({ steps, currentStep }) => {
  return (
    <div className="bg-gray-50 border-b border-gray-200">
      <div className="px-6 py-2">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <div className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}>
                <div className={`
                  relative flex items-center justify-center w-8 h-8 rounded-full transition-colors
                  ${currentStep > step.number
                    ? 'bg-green-500 text-white'
                    : currentStep === step.number
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-300 text-gray-500'
                  }
                `}>
                  {currentStep > step.number ? (
                    <Check size={16} />
                  ) : (
                    <step.icon size={16} />
                  )}
                </div>
                <span className={`
                  ml-2 text-sm font-medium
                  ${currentStep >= step.number ? 'text-gray-900' : 'text-gray-500'}
                `}>
                  {step.title}
                </span>
              </div>
              {index < steps.length - 1 && (
                <ChevronRight size={16} className="mx-3 text-gray-400" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
