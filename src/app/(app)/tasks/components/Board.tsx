"use client";
import { DragDropContext } from "@hello-pangea/dnd";
import type { Task } from "../types";
import Column from "./Column";
import { useMutation } from "convex/react";
import { useBoardData } from "./useBoardData";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { TaskFormDialog } from "./TaskFormDialog";
import { api } from "@/../convex/_generated/api";
import type { BoardData } from "../types";

const Board = (props: { boardData: BoardData | undefined }) => {
  const { boardData } = props;
  const changeTaskStatus = useMutation(api.tasks.tasksMutations.updateTask);

  const { localBoardData, onDragEnd } = useBoardData({
    boardData,
    changeTaskStatus,
  });

  if (!localBoardData) {
    return null;
  }

  return (
    <div className="w-full bg-transparent p-4">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-slate-800">Tasks Board</h1>
        <TaskFormDialog
          trigger={
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Create Task
            </Button>
          }
        />
      </div>
      <DragDropContext onDragEnd={onDragEnd}>
        <div className="grid grid-cols-1 gap-6 pb-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {localBoardData.columns.map((column) => {
            if (!column) return null;

            const tasks = column.taskIds
              .map((taskId: string) =>
                localBoardData.tasks.find((task) => task.id === taskId),
              )
              .filter((task): task is Task => task !== undefined);

            return <Column key={column.name} column={column} tasks={tasks} />;
          })}
        </div>
      </DragDropContext>
    </div>
  );
};

export default Board;
