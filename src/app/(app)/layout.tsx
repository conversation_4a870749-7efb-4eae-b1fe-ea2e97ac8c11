"use client";
import "@/styles/globals.css";
import "./styles.css";
import MainNav from "@/components/AppLayout/MainNav";
import { AppHeader } from "@/components/AppLayout/AppHeader";
import { CopilotSidebar } from "@copilotkit/react-ui";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <CopilotSidebar
      defaultOpen={true}
      clickOutsideToClose={false}
      instructions="You are an AI assistant helping with task management in Caliber. You can help users create, organize, and track their tasks and projects. Provide helpful guidance for project management, task prioritization, and team collaboration."
      labels={{
        title: "Caliber Assistant",
        initial: "How can I help you manage your tasks and projects today?",
      }}

    >
      <AppHeader />
      <nav aria-label="Primary">
        <MainNav />
      </nav>
      <div className="relative min-h-screen w-full">
        <div className="relative z-10 container mx-auto pt-16 pl-16 sm:pl-24">
          {children}
        </div>
      </div>
    </CopilotSidebar>
  );
}
