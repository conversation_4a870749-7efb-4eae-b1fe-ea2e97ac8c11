// src/components/entities/AddEntityWizard/components/WizardHeader.tsx

import React from 'react';
import { X } from 'lucide-react';

interface WizardHeaderProps {
  title: string;
  onClose: () => void;
}

export const WizardHeader: React.FC<WizardHeaderProps> = ({ title, onClose }) => {
  return (
    <div className="bg-white border-b border-gray-200">
      <div className="px-6 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors p-1 hover:bg-gray-100 rounded-full"
            aria-label="Close wizard"
          >
            <X size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};
