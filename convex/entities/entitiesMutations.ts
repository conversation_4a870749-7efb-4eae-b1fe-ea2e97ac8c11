// convex/entities/entitiesMutations.ts
import { mutation } from "../_generated/server";
import { v, ConvexError } from "convex/values";
import type { Id } from "../_generated/dataModel";
import { getAuthenticatedOrgContext } from "../auth";

export const createEntity = mutation({
  args: {
    entity_type: v.union(v.literal("person"), v.literal("organization")),
    entity_display_name: v.string(),
    primary_email: v.optional(v.string()),
    primary_phone: v.optional(v.string()),
    // Person-specific fields
    person_first_name: v.optional(v.string()),
    person_last_name: v.optional(v.string()),
    // Organization-specific fields (none required for basic creation)
  },
  returns: v.id("entities"),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);

    // Validate required fields based on entity type
    if (args.entity_type === "person" && !args.person_first_name) {
      throw new ConvexError("First name is required for person entities");
    }

    // Create the entity record
    const entityId = await ctx.db.insert("entities", {
      clerkTenantId,
      entity_type: args.entity_type,
      entity_display_name: args.entity_display_name,
      primary_email: args.primary_email,
      primary_phone: args.primary_phone,
    });

    // Create corresponding detail record
    if (args.entity_type === "person") {
      await ctx.db.insert("person_details", {
        clerkTenantId,
        entity_id: entityId,
        person_first_name: args.person_first_name!,
        person_last_name: args.person_last_name,
      });
    } else if (args.entity_type === "organization") {
      await ctx.db.insert("organization_details", {
        clerkTenantId,
        entity_id: entityId,
      });
    }

    return entityId;
  },
});


export const createEntityFromWizard = mutation({
  args: {
    entityType: v.union(v.literal("person"), v.literal("organization")),
    formData: v.object({
      // Person fields (optional since they may not be present for organizations)
      person_first_name: v.optional(v.string()),
      person_last_name: v.optional(v.string()),
      person_category: v.optional(v.union(
        v.literal("client"),
        v.literal("client_family"),
        v.literal("potential_client"),
        v.literal("service_provider"),
        v.literal("business_contact")
      )),
      person_birthday: v.optional(v.string()),
      person_notes: v.optional(v.string()),
      
      // Organization fields (optional since they may not be present for persons)
      organization_name: v.optional(v.string()),
      organization_category: v.optional(v.union(
        v.literal("client"),
        v.literal("service_provider")
      )),
      organization_industry: v.optional(v.string()),
      organization_employees_amount: v.optional(v.string()),
      organization_annual_revenue: v.optional(v.string()),
      organization_notes: v.optional(v.string()),
      
      // Shared fields
      primary_email: v.string(),
      primary_phone: v.string(),
      primary_address: v.string(),
      entity_links: v.array(v.object({
        type: v.union(v.literal("linkedin"), v.literal("domain_name"), v.literal("socialmedia")),
        url: v.string(),
      })),
      entity_image_url: v.optional(v.string()),
      client_type: v.optional(v.string()),
    }),
    relationships: v.array(v.object({
      id: v.union(v.id("entities"), v.string()), // Allow temporary string IDs
      entity: v.object({
        id: v.union(v.id("entities"), v.string()), // Allow temporary string IDs
        name: v.string(),
        type: v.union(v.literal("person"), v.literal("organization")),
        subtext: v.string(),
      }),
      relationshipType: v.string(),
    })),
    displayName: v.string(),
  },
  returns: v.id("entities"),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);
    const { entityType, formData } = args;

    // Validate required fields based on entity type
    if (entityType === "person") {
      if (!formData.person_first_name?.trim()) {
        throw new ConvexError("First name is required for person entities");
      }
    } else if (entityType === "organization") {
      if (!formData.organization_name?.trim()) {
        throw new ConvexError("Organization name is required for organization entities");
      }
    }

    // Determine category for client record creation
    const category = entityType === "person" ? formData.person_category : formData.organization_category;
    
    // Create the entity record
    const entityId = await ctx.db.insert("entities", {
      clerkTenantId,
      entity_type: entityType,
      entity_display_name: args.displayName,
      primary_email: formData.primary_email ?? undefined,
      primary_phone: formData.primary_phone ?? undefined,
      primary_address: formData.primary_address ?? undefined,
      entity_links: formData.entity_links.length > 0 ? formData.entity_links : undefined,
      entity_image_url: formData.entity_image_url,
    });

    // Create type-specific detail record
    if (entityType === "person") {
      // For person entities, we know these fields are required by the person schema
      await ctx.db.insert("person_details", {
        clerkTenantId,
        entity_id: entityId,
        person_first_name: formData.person_first_name!,
        person_last_name: formData.person_last_name ?? undefined,
        person_birthday: formData.person_birthday ? new Date(formData.person_birthday).getTime() : undefined,
        person_notes: formData.person_notes ?? undefined,
        person_emails: formData.primary_email ? [{ email: formData.primary_email, label: "primary" as const }] : undefined,
        person_phones: formData.primary_phone ? [{ number: formData.primary_phone, label: "primary" as const }] : undefined,
        person_addresses: formData.primary_address ? [{ line: formData.primary_address, label: "primary" as const }] : undefined,
      });
    } else if (entityType === "organization") {
      // For organization entities, we know these fields are required by the organization schema
      await ctx.db.insert("organization_details", {
        clerkTenantId,
        entity_id: entityId,
        organization_notes: formData.organization_notes ?? undefined,
        organization_industry: formData.organization_industry ?? undefined,
        organization_employees_amount: formData.organization_employees_amount ? parseInt(formData.organization_employees_amount) : undefined,
        organization_annual_revenue: formData.organization_annual_revenue ? parseFloat(formData.organization_annual_revenue) : undefined,
        organization_phones: formData.primary_phone ? [{ number: formData.primary_phone, label: "primary" as const }] : undefined,
        organization_addresses: formData.primary_address ? [{ line: formData.primary_address, label: "primary" as const }] : undefined,
      });
    }

    // Create client record if category indicates client status
    if (category === "client" || category === "potential_client") {
      await ctx.db.insert("clients", {
        clerkTenantId,
        entity_id: entityId,
        client_status: category === "client" ? "active" as const : "prospect" as const,
        client_communication_frequency: "monthly" as const, // Default value
      });
    }

    // Handle relationships creation - create temporary entities and establish relationships
    const createdEntityIds = new Map<string, Id<"entities">>(); // Map temp IDs to real IDs
    
    for (const relationship of args.relationships) {
      let relatedEntityId: Id<"entities">;
      
      // Check if this is a temporary entity (starts with "temp_")
      if (typeof relationship.entity.id === "string" && relationship.entity.id.startsWith("temp_")) {
        // Check if we already created this temp entity
        if (createdEntityIds.has(relationship.entity.id)) {
          relatedEntityId = createdEntityIds.get(relationship.entity.id)!;
        } else {
          // Create the temporary entity in the database
          const tempEntityId = await ctx.db.insert("entities", {
            clerkTenantId,
            entity_type: relationship.entity.type,
            entity_display_name: relationship.entity.name,
            primary_email: relationship.entity.subtext !== `New ${relationship.entity.type}` ? relationship.entity.subtext : undefined,
          });
          
          // Create corresponding detail record for temp entity
          if (relationship.entity.type === "person") {
            const nameParts = relationship.entity.name.split(" ");
            await ctx.db.insert("person_details", {
              clerkTenantId,
              entity_id: tempEntityId,
              person_first_name: nameParts[0] ?? relationship.entity.name,
              person_last_name: nameParts.slice(1).join(" ") || undefined,
            });
          } else if (relationship.entity.type === "organization") {
            await ctx.db.insert("organization_details", {
              clerkTenantId,
              entity_id: tempEntityId,
            });
          }
          
          createdEntityIds.set(relationship.entity.id, tempEntityId);
          relatedEntityId = tempEntityId;
        }
      } else {
        // This is an existing entity
        relatedEntityId = relationship.entity.id as Id<"entities">;
      }
      
      // Create the relationship record
      await ctx.db.insert("entity_relationships", {
        clerkTenantId,
        entity_1_id: entityId,
        entity_2_id: relatedEntityId,
        relationship_type: relationship.relationshipType,
        relationship_active: true,
      });
    }

    return entityId;
  },
});

export const updateEntityImage = mutation({
  args: {
    id: v.id("entities"),
    imageUrl: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { clerkTenantId } = await getAuthenticatedOrgContext(ctx);

    const entity = await ctx.db.get(args.id);
    if (!entity) {
      throw new ConvexError("Entity not found");
    }

    if (entity.clerkTenantId !== clerkTenantId) {
      throw new ConvexError("Unauthorized");
    }

    await ctx.db.patch(args.id, { entity_image_url: args.imageUrl });
    return null;
  },
});
