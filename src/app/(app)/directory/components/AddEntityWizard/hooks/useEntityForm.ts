"use client";

// src/app/(app)/directory/components/AddEntityWizard/hooks/useEntityForm.ts

import { useState, useCallback } from 'react';
import type { EntityFormData, FormErrors, EntityType, PersonCategory, OrganizationCategory } from '../AddEntityWizard.types';
import { INITIAL_FORM_DATA } from '../AddEntityWizard.constants';

export const useEntityForm = () => {
  const [formData, setFormData] = useState<EntityFormData>(INITIAL_FORM_DATA);
  const [errors, setErrors] = useState<FormErrors>({});

  const updateFormData = useCallback((updates: Partial<EntityFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    // Clear errors for updated fields
    const updatedFields = Object.keys(updates);
    setErrors(prev => {
      const newErrors = { ...prev };
      updatedFields.forEach(field => delete newErrors[field]);
      return newErrors;
    });
  }, []);

  const validateStep = useCallback((step: number, entityType: EntityType): boolean => {
    const newErrors: FormErrors = {};

    if (entityType === 'person') {
      switch (step) {
        case 2:
          if (!formData.person_first_name.trim()) {
            newErrors.person_first_name = 'First name is required';
          }
          if (formData.primary_email && !isValidEmail(formData.primary_email)) {
            newErrors.primary_email = 'Please enter a valid email';
          }
          break;
      }
    } else {
      switch (step) {
        case 2:
          if (!formData.organization_name.trim()) {
            newErrors.organization_name = 'Organization name is required';
          }
          if (formData.primary_email && !isValidEmail(formData.primary_email)) {
            newErrors.primary_email = 'Please enter a valid email';
          }
          break;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const resetForm = useCallback(() => {
    setFormData(INITIAL_FORM_DATA);
    setErrors({});
  }, []);

  const getDisplayName = useCallback((): string => {
    if (formData.person_first_name || formData.person_last_name) {
      return `${formData.person_first_name} ${formData.person_last_name}`.trim();
    }
    return formData.organization_name;
  }, [formData]);

  return {
    formData,
    errors,
    updateFormData,
    validateStep,
    resetForm,
    getDisplayName
  };
};

// Helper function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};