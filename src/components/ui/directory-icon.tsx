import React from 'react';

interface DirectoryIconProps {
  size?: number;
  className?: string;
}

export const DirectoryIcon: React.FC<DirectoryIconProps> = ({ size = 20, className = "" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      {/* Building/Organization on the left */}
      <rect x="2" y="8" width="6" height="12" rx="1" />
      <rect x="3" y="10" width="1" height="1" />
      <rect x="5" y="10" width="1" height="1" />
      <rect x="3" y="12" width="1" height="1" />
      <rect x="5" y="12" width="1" height="1" />
      <rect x="3" y="14" width="1" height="1" />
      <rect x="5" y="14" width="1" height="1" />
      
      {/* People/Cards on the right */}
      <rect x="10" y="4" width="12" height="7" rx="1" />
      <rect x="10" y="13" width="12" height="7" rx="1" />
      
      {/* Person silhouette in top card */}
      <circle cx="13" cy="6.5" r="1" />
      <path d="M11.5 9.5h3v1h-3z" />
      
      {/* Person silhouette in bottom card */}
      <circle cx="13" cy="15.5" r="1" />
      <path d="M11.5 18.5h3v1h-3z" />
      
      {/* Connection lines */}
      <path d="M8 12h2" strokeDasharray="2,2" opacity="0.6" />
      <path d="M8 16h2" strokeDasharray="2,2" opacity="0.6" />
    </svg>
  );
};