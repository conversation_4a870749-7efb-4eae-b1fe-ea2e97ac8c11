---
description: 
globs: 
alwaysApply: false
---
Webhooks overview
A webhook is an event-driven method of communication between applications.

Unlike typical APIs where you would need to poll for data very frequently to get it "real-time", webhooks only send data when there is an event to trigger the webhook. This makes webhooks seem "real-time", but it's important to note that they are asynchronous.

For example, if you are onboarding a new user, you can't rely on the webhook delivery as part of that flow. Typically the delivery will happen quickly, but it's not guaranteed to be delivered immediately or at all. Webhooks are best used for things like sending a notification or updating a database, but not for synchronous flows where you need to know the webhook was delivered before moving on to the next step. If you need a synchronous flow, see the onboarding guide for an example.

Clerk webhooks
Clerk webhooks allow you to receive event notifications from <PERSON>, such as when a user is created or updated. When an event occurs, Clerk will send an HTTP POST request to your webhook endpoint configured for the event type. The payload carries a JSON object. You can then use the information from the request's JSON payload to trigger actions in your app, such as sending a notification or updating a database.

Clerk uses Svix to send our webhooks.

You can find the Webhook signing secret when you select the endpoint you created on the Webhooks page in the Clerk Dashboard.

Supported webhook events
To find a list of all the events Clerk supports:

In the Clerk Dashboard, navigate to the Webhooks page.
Select the Event Catalog tab.
Payload structure
The payload of a webhook is a JSON object that contains the following properties:

data: contains the actual payload sent by Clerk. The payload can be a different object depending on the event type. For example, for user.* events, the payload will always be the User object.
object: always set to event.
type: the type of event that triggered the webhook.
timestamp: timestamp in milliseconds of when the event occurred.
instance_id: the identifier of your Clerk instance.
The following example shows the payload of a user.created event:


{
  "data": {
    "birthday": "",
    "created_at": *************,
    "email_addresses": [
      {
        "email_address": "<EMAIL>",
        "id": "idn_29w83yL7CwVlJXylYLxcslromF1",
        "linked_to": [],
        "object": "email_address",
        "verification": {
          "status": "verified",
          "strategy": "ticket"
        }
      }
    ],
    "external_accounts": [],
    "external_id": "567772",
    "first_name": "Example",
    "gender": "",
    "id": "user_29w83sxmDNGwOuEthce5gg56FcC",
    "image_url": "https://img.clerk.com/xxxxxx",
    "last_name": "Example",
    "last_sign_in_at": *************,
    "object": "user",
    "password_enabled": true,
    "phone_numbers": [],
    "primary_email_address_id": "idn_29w83yL7CwVlJXylYLxcslromF1",
    "primary_phone_number_id": null,
    "primary_web3_wallet_id": null,
    "private_metadata": {},
    "profile_image_url": "https://www.gravatar.com/avatar?d=mp",
    "public_metadata": {},
    "two_factor_enabled": false,
    "unsafe_metadata": {},
    "updated_at": *************,
    "username": null,
    "web3_wallets": []
  },
  "instance_id": "ins_123",
  "object": "event",
  "timestamp": *************,
  "type": "user.created"
}
The payload should always be treated as unsafe until you validate the incoming webhook. Webhooks will originate from another server and be sent to your application as a POST request. A bad actor would fake a webhook event to try and gain access to your application or data.

How Clerk handles delivery issues
Retry
Svix will use a set schedule and retry any webhooks that fail. To see the up-to-date schedule, see the Svix Retry Schedule.

If Svix is attempting and failing to send a webhook, and that endpoint is removed or disabled from the Webhooks page in the Clerk Dashboard, then the attempts will also be disabled.

Replay
If a webhook message or multiple webhook messages fail to send, you have the option to replay the webhook messages. This protects against your service having downtime or against a misconfigured endpoint.

To replay webhook messages:

In the Clerk Dashboard, navigate to the Webhooks page.
Select the affected endpoint.
In the Message Attempts section, next to the message you want to replay, select the menu icon on the right side, and then select Replay.
The Replay Messages menu will appear. You can choose to:
Resend the specific message you selected.
Resend all failed messages since the first failed message in that date range.
Resend all missing messages since the first failed message in that date range.
Sync data to your database
You can find a guide on how to use webhooks to sync your data to your database here.

Protect your webhooks from abuse
To ensure that the API route receiving the webhook can only be hit by your app, there are a few protections you can put in place:

Verify the request signature: Svix webhook requests are signed and can be verified to ensure the request is not malicious. To learn more, see Svix's guide on how to verify webhooks with the svix libraries or how to verify webhooks manually.

Only accept requests coming from Svix's webhook IPs: To further prevent attackers from flooding your servers or wasting your compute, you can ensure that your webhook-receiving api routes only accept requests coming from Svix's webhook IPs, rejecting all other requests.

Sync Clerk data to your app with webhooks
Before you start
A Clerk app is required.
A ngrok account is required.
The recommended way to sync Clerk data to your app is through webhooks.

In this guide, you'll set up a webhook in your app to listen for the user.created event, create an endpoint in the Clerk Dashboard, build a handler for verifying the webhook, and test it locally using ngrok and the Clerk Dashboard.

Clerk offers many events, but three key events include:

user.created: Triggers when a new user registers in the app or is created via the Clerk Dashboard or Backend API. Listening to this event allows the initial insertion of user information in your database.
user.updated: Triggers when user information is updated via Clerk components, the Clerk Dashboard, or Backend API. Listening to this event keeps data synced between Clerk and your external database. It is recommended to only sync what you need to simplify this process.
user.deleted: Triggers when a user deletes their account, or their account is removed via the Clerk Dashboard or Backend API. Listening to this event allows you to delete the user from your database or add a deleted: true flag.
These steps apply to any Clerk event. To make the setup process easier, it's recommended to keep two browser tabs open: one for your Clerk Webhooks page and one for your ngrok dashboard.

Set up ngrok
To test a webhook locally, you need to expose your local server to the internet. This guide uses ngrok which creates a forwarding URL that sends the webhook payload to your local server.

Navigate to the ngrok dashboard to create an account.
On the ngrok dashboard homepage, follow the setup guide instructions. Under Deploy your app online, select Static domain. Run the provided command, replacing the port number with your server's port. For example, if your development server runs on port 3000, the command should resemble ngrok http --url=<YOUR_FORWARDING_URL> 3000. This creates a free static domain and starts a tunnel.
Save your Forwarding URL somewhere secure.
Set up a webhook endpoint
In the Clerk Dashboard, navigate to the Webhooks page.
Select Add Endpoint.
In the Endpoint URL field, paste the ngrok Forwarding URL you saved earlier, followed by /api/webhooks. This is the endpoint that Clerk uses to send the webhook payload. The full URL should resemble https://fawn-two-nominally.ngrok-free.app/api/webhooks.
In the Subscribe to events section, scroll down and select user.created.
Select Create. You'll be redirected to your endpoint's settings page. Keep this page open.
Add your Signing Secret to .env
To verify the webhook payload, you'll need your endpoint's Signing Secret. Since you don't want this secret exposed in your codebase, store it as an environment variable in your .env file during local development.

On the endpoint's settings page in the Clerk Dashboard, copy the Signing Secret. You may need to select the eye icon to reveal the secret.
In your project's root directory, open or create an .env file, which should already include your Clerk API keys. Assign your Signing Secret to CLERK_WEBHOOK_SIGNING_SECRET. The file should resemble:
.env

Caliber

Caliber


NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_bG95YWwtY29sdC0yOC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_Nl58AELEyzU3aiGx3U7DomTxMjHCQIA7tZAIj67VJT
CLERK_WEBHOOK_SIGNING_SECRET=whsec_123
Make sure the webhook route is public
Incoming webhook events don't contain auth information. They come from an external source and aren't signed in or out, so the route must be public to allow access. If you're using clerkMiddleware(), ensure that the /api/webhooks(.*) route is set as public. For information on configuring routes, see the clerkMiddleware() guide.

Create a route handler to verify the webhook
Set up a Route Handler that uses Clerk's verifyWebhook() function to verify the incoming Clerk webhook and process the payload.

For this guide, the payload will be logged to the console. In a real app, you'd use the payload to trigger an action. For example, if listening for the user.created event, you might perform a database create or upsert to add the user's Clerk data to your database's user table.

If the route handler returns a 4xx or 5xx code, or no code at all, the webhook event will be retried. If the route handler returns a 2xx code, the event will be marked as successful, and retries will stop.

Note

The following Route Handler can be used for any webhook event you choose to listen to. It is not specific to user.created.

Next.js
Astro
Express
Fastify
Nuxt
React Router
TanStack React Start
app/api/webhooks/route.ts

import { verifyWebhook } from '@clerk/nextjs/webhooks'
import { NextRequest } from 'next/server'

export async function POST(req: NextRequest) {
  try {
    const evt = await verifyWebhook(req)

    // Do something with payload
    // For this guide, log payload to console
    const { id } = evt.data
    const eventType = evt.type
    console.log(`Received webhook with ID ${id} and event type of ${eventType}`)
    console.log('Webhook payload:', evt.data)

    return new Response('Webhook received', { status: 200 })
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return new Response('Error verifying webhook', { status: 400 })
  }
}
Narrow to a webhook event for type inference
WebhookEvent encompasses all possible webhook types. Narrow down the event type for accurate typing for specific events.

In the following example, the if statement narrows the type to user.created, enabling type-safe access to evt.data with autocompletion.

app/api/webhooks/route.ts

console.log(`Received webhook with ID ${id} and event type of ${eventType}`)
console.log('Webhook payload:', body)

if (evt.type === 'user.created') {
  console.log('userId:', evt.data.id)
}
To handle types manually, import the following types from your backend SDK (e.g., @clerk/nextjs/webhooks):

DeletedObjectJSON
EmailJSON
OrganizationInvitationJSON
OrganizationJSON
OrganizationMembershipJSON
SessionJSON
SMSMessageJSON
UserJSON
Test the webhook
Start your Next.js server.
In your endpoint's settings page in the Clerk Dashboard, select the Testing tab.
In the Select event dropdown, select user.created.
Select Send Example.
In the Message Attempts section, confirm that the event's Status is labeled with Succeeded. In your server's terminal where your app is running, you should see the webhook's payload.
Handling failed messages
In the Message Attempts section, select the event whose Status is labeled with Failed.
Scroll down to the Webhook Attempts section.
Toggle the arrow next to the Status column.
Review the error. Solutions vary by error type. For more information, refer to the guide on debugging your webhooks.
Trigger the webhook
To trigger the user.created event, create a new user in your app.

In the terminal where your app is running, you should see the webhook's payload logged. You can also check the Clerk Dashboard to see the webhook attempt, the same way you did when testing the webhook.

Configure your production instance
When you're ready to deploy your app to production, follow the guide on deploying your Clerk app to production.
Create your production webhook by following the steps in the previous Set up a webhook endpoint section. In the Endpoint URL field, instead of pasting the ngrok URL, paste your production app URL.
After you've set up your webhook endpoint, you'll be redirected to your endpoint's settings page. Copy the Signing Secret.
On your hosting platform, update your environment variables on your hosting platform by adding Signing Secret with the key of CLERK_WEBHOOK_SIGNING_SECRET.
Redeploy your app.